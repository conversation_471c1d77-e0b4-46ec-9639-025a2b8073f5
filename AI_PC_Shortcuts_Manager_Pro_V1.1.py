import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import subprocess
import webbrowser
from datetime import datetime, timedelta
import threading
import re
import sys
from pathlib import Path
import collections # For analytics

# Try to import the keyboard library for global hotkeys
# This library might require administrative privileges on some systems.
try:
    import keyboard
    GLOBAL_HOTKEYS_AVAILABLE = True
except ImportError:
    GLOBAL_HOTKEYS_AVAILABLE = False
    print("Warning: 'keyboard' library not found. Global hotkeys will be disabled.")
    print("To enable global hotkeys, install it: Keyboard library")

class ModernAIShortcutsManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AI Shortcuts Manager Pro - AMSSoftX")
        # Set initial window size and then maximize it
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700) # Ensure a reasonable minimum size
        self.root.state('zoomed') # Open in full-screen mode

        # Modern color scheme
        self.colors = {
            'primary': '#1e293b',      # Dark slate
            'secondary': '#334155',    # Slate
            'accent': '#3b82f6',       # Blue
            'success': '#10b981',       # Emerald
            'warning': '#f59e0b',      # Amber
            'danger': '#ef4444',       # Red
            'text': '#f8fafc',         # Slate 50
            'text_secondary': '#cbd5e1', # Slate 300
            'surface': '#475569',      # Slate 600
            'background': '#0f172a'    # Slate 900
        }

        self.root.configure(bg=self.colors['primary'])

        # Data storage paths
        self.app_dir = Path.home() / ".amssoftx" / "ai_shortcuts"
        self.app_dir.mkdir(parents=True, exist_ok=True)
        self.shortcuts_file = self.app_dir / "shortcuts_data.json"
        self.settings_file = self.app_dir / "settings.json"
        self.analytics_file = self.app_dir / "analytics_data.json" # For persistent analytics

        # Load application data
        self.shortcuts = self.load_shortcuts()
        self.settings = self.load_settings()
        self.usage_stats = self.load_analytics() # Load analytics data
        self.ai_suggestions = [] # This will be populated by AI assistant

        # Hotkey management variables
        self.hotkey_listener_thread = None
        self.hotkey_running = False
        self.registered_hotkeys = {} # To keep track of hotkeys registered with `keyboard`

        # Initialize UI components
        self.setup_styles()
        self.setup_ui()
        self.load_system_info()

        # Start auto-save timer
        self.auto_save_timer()

        # Start global hotkey listener if enabled
        if GLOBAL_HOTKEYS_AVAILABLE and self.settings.get('hotkeys_enabled', True):
            self._start_hotkey_listener()

        # Ensure hotkey listener is stopped on app close
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

    def _on_closing(self):
        """Handle application closing, stopping hotkey listener."""
        if self.hotkey_running:
            self._stop_hotkey_listener()
        self.root.destroy()

    def load_shortcuts(self):
        """Loads shortcuts from the JSON file."""
        if self.shortcuts_file.exists():
            try:
                with open(self.shortcuts_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading shortcuts: {e}")
                messagebox.showerror("Load Error", f"Failed to load shortcuts: {e}")
                return {}
        return {}

    def load_analytics(self):
        """Loads analytics data from the JSON file."""
        if self.analytics_file.exists():
            try:
                with open(self.analytics_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading analytics: {e}")
                return {}
        return {}

    def save_analytics(self):
        """Saves analytics data to the JSON file."""
        try:
            with open(self.analytics_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_stats, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving analytics: {e}")
            messagebox.showerror("Save Error", f"Failed to save analytics: {e}")

    def load_settings(self):
        """Loads application settings from the JSON file."""
        default_settings = {
            'theme': 'dark',
            'auto_save': True,
            'startup_tab': 'dashboard',
            'show_notifications': True,
            'auto_update_suggestions': True, # For AI tab
            'backup_enabled': True,
            'hotkeys_enabled': True,
            'help_language': 'English' # Default help language
        }

        if self.settings_file.exists():
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    default_settings.update(loaded_settings)
            except Exception as e:
                print(f"Error loading settings: {e}")
        return default_settings

    def save_shortcuts(self):
        """Saves shortcuts to the JSON file and creates a backup if enabled."""
        try:
            with open(self.shortcuts_file, 'w', encoding='utf-8') as f:
                json.dump(self.shortcuts, f, indent=2, ensure_ascii=False)

            # Create backup if enabled
            if self.settings.get('backup_enabled', True):
                backup_file = self.app_dir / f"shortcuts_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(self.shortcuts, f, indent=2, ensure_ascii=False)

                # Keep only last 5 backups
                backups = sorted(self.app_dir.glob("shortcuts_backup_*.json"))
                for old_backup in backups[:-5]:
                    old_backup.unlink()

        except Exception as e:
            messagebox.showerror("Error", f"Error saving shortcuts: {str(e)}")

    def save_settings(self):
        """Saves application settings to the JSON file."""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving settings: {e}")

    def setup_styles(self):
        """Sets up modern ttk styles for a consistent look."""
        self.style = ttk.Style()
        self.style.theme_use('clam') # 'clam' is a good base for customization

        # Configure Notebook (Tabs)
        self.style.configure('Modern.TNotebook',
                             background=self.colors['primary'],
                             borderwidth=0)
        self.style.configure('Modern.TNotebook.Tab',
                             background=self.colors['secondary'],
                             foreground=self.colors['text'],
                             padding=[20, 10],
                             font=('Segoe UI', 10))
        self.style.map('Modern.TNotebook.Tab',
                       background=[('selected', self.colors['accent']),
                                   ('active', self.colors['surface'])],
                       foreground=[('selected', 'white'),
                                   ('active', self.colors['text'])])

        # Configure Frames
        self.style.configure('Modern.TFrame',
                             background=self.colors['secondary'])

        # Configure Buttons
        self.style.configure('Modern.TButton',
                             background=self.colors['accent'],
                             foreground='white',
                             font=('Segoe UI', 10, 'bold'), # Made font bold for buttons
                             padding=[15, 8],
                             relief=tk.FLAT,
                             border=0)
        self.style.map('Modern.TButton',
                       background=[('active', self.adjust_color(self.colors['accent'], -20)),
                                   ('pressed', self.adjust_color(self.colors['accent'], -40))])

        # Configure Combobox
        self.style.configure('TCombobox',
                             fieldbackground=self.colors['background'],
                             background=self.colors['surface'],
                             foreground=self.colors['text'],
                             selectbackground=self.colors['accent'],
                             selectforeground='white',
                             bordercolor=self.colors['surface'],
                             arrowcolor=self.colors['text'],
                             relief=tk.FLAT)
        self.style.map('TCombobox',
                       fieldbackground=[('readonly', self.colors['background'])],
                       background=[('readonly', self.colors['surface'])])

        # Configure Scrollbar for better aesthetics
        self.style.configure('Vertical.TScrollbar',
                             background=self.colors['surface'],
                             troughcolor=self.colors['background'],
                             bordercolor=self.colors['surface'],
                             arrowcolor=self.colors['text'],
                             gripcount=0, # Hide grip for a cleaner look
                             relief=tk.FLAT)
        self.style.map('Vertical.TScrollbar',
                       background=[('active', self.adjust_color(self.colors['surface'], -10))])


    def setup_ui(self):
        """Sets up the main user interface layout."""
        # Header frame (branding and quick actions)
        self.create_header()

        # Main content area (for notebook tabs)
        main_container = tk.Frame(self.root, bg=self.colors['primary'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # Create modern notebook (tabbed interface)
        self.notebook = ttk.Notebook(main_container, style='Modern.TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Status bar - Crucially, create this BEFORE any tab that might update it
        self.create_status_bar()

        # Create all the main application tabs
        self.create_dashboard_tab()
        self.create_shortcuts_tab()
        self.create_ai_tab()
        self.create_analytics_tab()
        self.create_settings_tab()
        self.create_help_tab()

        # Bind global hotkey for refreshing data (F5) and adding new shortcut (Ctrl+N)
        self.root.bind('<Control-n>', lambda e: self.notebook.select(1)) # Select Shortcuts tab
        self.root.bind('<F5>', lambda e: self.refresh_all_data())
        # Bind notebook tab change event to refresh AI suggestions on dashboard
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_change)

    def on_tab_change(self, event):
        """Handles tab change events to refresh specific tab contents."""
        selected_tab = self.notebook.tab(self.notebook.select(), "text")
        if selected_tab == "📊 Dashboard":
            self.generate_ai_suggestions(update_dashboard=True) # Refresh AI suggestions for dashboard

    def create_header(self):
        """Creates the application header with branding and quick action buttons."""
        header_frame = tk.Frame(self.root, bg=self.colors['background'], height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False) # Prevent frame from resizing to fit content

        # Company branding (logo and title)
        branding_frame = tk.Frame(header_frame, bg=self.colors['background'])
        branding_frame.pack(side=tk.LEFT, padx=20, pady=15)

        # Logo placeholder (a simple 'AI' text for now) - Changed to a more distinct AI icon
        logo_frame = tk.Frame(branding_frame, bg=self.colors['accent'], width=50, height=50, relief=tk.FLAT, bd=0, highlightthickness=0,
                              highlightbackground=self.colors['accent'], highlightcolor=self.colors['accent'])
        logo_frame.pack(side=tk.LEFT, padx=(0, 15))
        logo_frame.pack_propagate(False)
        # Using a unicode character for AI icon (a lightbulb or brain could also work)
        tk.Label(logo_frame, text="💡", bg=self.colors['accent'], fg='white',
                 font=('Segoe UI', 24, 'bold')).place(relx=0.5, rely=0.5, anchor='center') # Increased font size for icon

        # Title and company info
        title_frame = tk.Frame(branding_frame, bg=self.colors['background'])
        title_frame.pack(side=tk.LEFT)
        tk.Label(title_frame, text="AI Shortcuts Manager Pro",
                 font=('Segoe UI', 18, 'bold'), fg=self.colors['text'],
                 bg=self.colors['background']).pack(anchor='w')
        tk.Label(title_frame, text="Developed & Powered by AMSSoftX • https://amssoftx.com",
                 font=('Segoe UI', 10), fg=self.colors['text_secondary'],
                 bg=self.colors['background']).pack(anchor='w')

        # Quick action buttons
        actions_frame = tk.Frame(header_frame, bg=self.colors['background'])
        actions_frame.pack(side=tk.RIGHT, padx=20, pady=15)
        self.create_modern_button(actions_frame, "🔄 Sync", self.sync_shortcuts,
                                  bg=self.colors['accent']).pack(side=tk.RIGHT, padx=5)
        self.create_modern_button(actions_frame, "⚡ Quick Add", self.show_add_shortcut_dialog,
                                  bg=self.colors['success']).pack(side=tk.RIGHT, padx=5)

    def create_modern_button(self, parent, text, command, bg=None, fg='white', width=None):
        """Helper function to create a consistently styled modern button."""
        if bg is None:
            bg = self.colors['accent']
        
        btn = tk.Button(parent, text=text, command=command,
                        bg=bg, fg=fg, font=('Segoe UI', 10, 'bold'), # Use bold font for consistency
                        relief=tk.FLAT, padx=15, pady=8,
                        cursor='hand2', width=width,
                        activebackground=self.adjust_color(bg, -20),
                        activeforeground=fg, border=0)
        
        # Add hover effects for visual feedback
        def on_enter(e):
            btn.configure(bg=self.adjust_color(bg, -20))
        def on_leave(e):
            btn.configure(bg=bg)
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        return btn

    def adjust_color(self, color, amount):
        """Adjusts the brightness of a hex color for hover/active states."""
        if color.startswith('#'):
            color = color[1:]
        try:
            rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
            rgb = tuple(max(0, min(255, c + amount)) for c in rgb)
            return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
        except:
            return color # Return original color if conversion fails

    def create_dashboard_tab(self):
        """Creates the Dashboard tab with overview stats and recent activity."""
        dashboard_frame = tk.Frame(self.notebook, bg=self.colors['secondary'])
        self.notebook.add(dashboard_frame, text="📊 Dashboard")

        # Configure dashboard_frame to expand with the window
        dashboard_frame.grid_rowconfigure(2, weight=1) # Row for activity_frame
        dashboard_frame.grid_columnconfigure(0, weight=1)

        # Welcome section
        welcome_frame = tk.Frame(dashboard_frame, bg=self.colors['secondary'])
        welcome_frame.grid(row=0, column=0, columnspan=2, sticky='ew', padx=30, pady=20)

        tk.Label(welcome_frame, text="Welcome to AMSSoftX AI Shortcuts Manager Pro",
                 font=('Segoe UI', 16, 'bold'), fg=self.colors['text'],
                 bg=self.colors['secondary']).pack(anchor='w')
        tk.Label(welcome_frame, text="Streamline your workflow with intelligent shortcut management",
                 font=('Segoe UI', 11), fg=self.colors['text_secondary'],
                 bg=self.colors['secondary']).pack(anchor='w', pady=(5, 0))

        # Stats cards container
        stats_container = tk.Frame(dashboard_frame, bg=self.colors['secondary'])
        stats_container.grid(row=1, column=0, columnspan=2, sticky='ew', padx=30, pady=(0, 20))

        # Create stats cards
        self.create_stats_card(stats_container, "Total Shortcuts", "0", "📱", 0)
        self.create_stats_card(stats_container, "Most Used", "None", "🔥", 1)
        self.create_stats_card(stats_container, "Categories", "0", "📂", 2)
        self.create_stats_card(stats_container, "Total Launches", "0", "🚀", 3)
        for i in range(4): # Configure columns within stats_container to expand
            stats_container.grid_columnconfigure(i, weight=1)

        # Recent activity section
        activity_frame = tk.Frame(dashboard_frame, bg=self.colors['secondary'])
        activity_frame.grid(row=2, column=0, sticky='nsew', padx=30, pady=(0, 20))

        tk.Label(activity_frame, text="Recent Activity",
                 font=('Segoe UI', 14, 'bold'), fg=self.colors['text'],
                 bg=self.colors['secondary']).pack(anchor='w', pady=(0, 10))

        # Activity listbox
        self.activity_listbox = tk.Listbox(activity_frame,
                                           bg=self.colors['surface'],
                                           fg=self.colors['text'],
                                           font=('Segoe UI', 10),
                                           relief=tk.FLAT,
                                           selectbackground=self.colors['accent'],
                                           height=8)
        self.activity_listbox.pack(fill=tk.BOTH, expand=True)
        # Bind click event to launch shortcut from dashboard
        self.activity_listbox.bind('<<ListboxSelect>>', self.on_dashboard_activity_select)


        # Quick actions panel
        self.create_quick_actions_panel(dashboard_frame)
        self.quick_actions_panel_frame.grid(row=2, column=1, sticky='nsew', padx=30, pady=(0, 20))

        # Make columns in dashboard_frame equal weight for better distribution
        dashboard_frame.grid_columnconfigure(0, weight=1)
        dashboard_frame.grid_columnconfigure(1, weight=1)

    def on_dashboard_activity_select(self, event):
        """Handles selection in the recent activity listbox to launch a shortcut."""
        selected_indices = self.activity_listbox.curselection()
        if not selected_indices:
            return
        
        selected_item = self.activity_listbox.get(selected_indices[0])
        # Expected format: "YYYY-MM-DD HH:MM - Launched: ShortcutName"
        # Extract "ShortcutName"
        match = re.search(r'Launched: (.+)', selected_item)
        if match:
            shortcut_name = match.group(1).strip()
            self.run_shortcut_by_name(shortcut_name)
        else:
            self.update_status("Could not parse shortcut name from activity log.")


    def create_stats_card(self, parent, title, value, icon, column):
        """Helper function to create a consistently styled stats card for the dashboard."""
        card = tk.Frame(parent, bg=self.colors['surface'], relief=tk.FLAT, bd=0)
        card.grid(row=0, column=column, padx=10, pady=10, sticky='nsew')
        parent.grid_columnconfigure(column, weight=1)

        content_frame = tk.Frame(card, bg=self.colors['surface'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

        top_frame = tk.Frame(content_frame, bg=self.colors['surface'])
        top_frame.pack(fill=tk.X)

        tk.Label(top_frame, text=icon, font=('Segoe UI', 20),
                 bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        value_label = tk.Label(top_frame, text=value, font=('Segoe UI', 18, 'bold'),
                               bg=self.colors['surface'], fg=self.colors['text'])
        value_label.pack(side=tk.RIGHT)

        tk.Label(content_frame, text=title, font=('Segoe UI', 10),
                 bg=self.colors['surface'], fg=self.colors['text_secondary']).pack(anchor='w', pady=(5, 0))

        # Store reference to update later
        setattr(self, f"stats_card_{column}", value_label)

    def create_quick_actions_panel(self, parent):
        """Creates the quick actions panel on the dashboard."""
        self.quick_actions_panel_frame = tk.Frame(parent, bg=self.colors['secondary'])

        tk.Label(self.quick_actions_panel_frame, text="Quick Actions",
                 font=('Segoe UI', 14, 'bold'), fg=self.colors['text'],
                 bg=self.colors['secondary']).pack(anchor='w', pady=(0, 10))

        buttons_frame = tk.Frame(self.quick_actions_panel_frame, bg=self.colors['secondary'])
        buttons_frame.pack(fill=tk.X)

        actions = [
            ("➕ Add Shortcut", self.show_add_shortcut_dialog, self.colors['success']),
            ("🔍 Search Apps", self.search_installed_apps, self.colors['accent']), # This will now open the new dialog
            ("📊 Generate Report", self.generate_usage_report, self.colors['warning']),
            ("🔄 Backup Data", self.backup_shortcuts, self.colors['surface']),
        ]

        for i, (text, command, color) in enumerate(actions):
            self.create_modern_button(buttons_frame, text, command, bg=color).grid(
                row=0, column=i, padx=5, sticky='ew')
            buttons_frame.grid_columnconfigure(i, weight=1)
        
        # AI recommendations section on Dashboard
        ai_recommendations_frame = tk.Frame(self.quick_actions_panel_frame, bg=self.colors['surface'], padx=15, pady=15)
        ai_recommendations_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        tk.Label(ai_recommendations_frame, text="AI Recommendations",
                 font=('Segoe UI', 12, 'bold'), fg=self.colors['text'],
                 bg=self.colors['surface']).pack(anchor='w', pady=(0, 10))
        
        self.dashboard_ai_suggestions_text = tk.Text(ai_recommendations_frame, wrap=tk.WORD, bg=self.colors['background'],
                                                    fg=self.colors['text'], font=('Segoe UI', 9), relief=tk.FLAT,
                                                    insertbackground=self.colors['text'], height=5)
        self.dashboard_ai_suggestions_text.pack(fill=tk.BOTH, expand=True)
        # Add a scrollbar to the dashboard AI suggestions text widget
        dashboard_ai_suggestions_scrollbar = ttk.Scrollbar(ai_recommendations_frame, command=self.dashboard_ai_suggestions_text.yview, style='Vertical.TScrollbar')
        dashboard_ai_suggestions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.dashboard_ai_suggestions_text.config(yscrollcommand=dashboard_ai_suggestions_scrollbar.set)

        # Add buttons for each AI recommendation on the dashboard
        self.dashboard_ai_add_buttons_frame = tk.Frame(ai_recommendations_frame, bg=self.colors['surface'])
        self.dashboard_ai_add_buttons_frame.pack(fill=tk.X, pady=(10, 0))


    def create_shortcuts_tab(self):
        """Creates the Shortcuts management tab with list/grid view, search, and filters."""
        shortcuts_frame = tk.Frame(self.notebook, bg=self.colors['secondary'])
        self.notebook.add(shortcuts_frame, text="⚡ Shortcuts")

        shortcuts_frame.grid_rowconfigure(1, weight=1) # Content frame row
        shortcuts_frame.grid_columnconfigure(0, weight=1) # The only column

        # Toolbar for search, filter, and actions
        toolbar = tk.Frame(shortcuts_frame, bg=self.colors['surface'], height=60)
        toolbar.grid(row=0, column=0, sticky='ew')
        toolbar.pack_propagate(False)

        # Search and filter controls
        search_frame = tk.Frame(toolbar, bg=self.colors['surface'])
        search_frame.pack(side=tk.LEFT, padx=20, pady=15)
        tk.Label(search_frame, text="🔍", font=('Segoe UI', 14),
                 bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_shortcuts)
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=('Segoe UI', 11), width=30,
                               bg=self.colors['background'], fg=self.colors['text'],
                               insertbackground=self.colors['text'], relief=tk.FLAT)
        search_entry.pack(side=tk.LEFT, padx=(10, 0))

        tk.Label(search_frame, text="Category:", font=('Segoe UI', 10),
                 bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 5))
        self.filter_var = tk.StringVar(value="All")
        filter_combo = ttk.Combobox(search_frame, textvariable=self.filter_var,
                                    values=["All", "Application", "Website", "File", "Folder", "System"],
                                    width=12, state="readonly", style='TCombobox')
        filter_combo.pack(side=tk.LEFT, padx=5)
        filter_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_shortcuts())

        # Action buttons
        actions_frame = tk.Frame(toolbar, bg=self.colors['surface'])
        actions_frame.pack(side=tk.RIGHT, padx=20, pady=15)
        self.create_modern_button(actions_frame, "➕ Add", self.show_add_shortcut_dialog,
                                  bg=self.colors['success']).pack(side=tk.RIGHT, padx=5)
        self.create_modern_button(actions_frame, "📤 Export", self.export_shortcuts,
                                  bg=self.colors['warning']).pack(side=tk.RIGHT, padx=5)
        self.create_modern_button(actions_frame, "📥 Import", self.import_shortcuts,
                                  bg=self.colors['accent']).pack(side=tk.RIGHT, padx=5)

        # Main content area for shortcuts display
        content_frame = tk.Frame(shortcuts_frame, bg=self.colors['secondary'])
        content_frame.grid(row=1, column=0, sticky='nsew', padx=20, pady=(0, 20))

        self.create_shortcuts_view(content_frame)

    def create_shortcuts_view(self, parent):
        """Creates the scrollable canvas and frame for displaying shortcuts in list view."""
        # Removed view mode selector (Grid/List) as per request

        # Canvas for scrollable content
        self.shortcuts_canvas = tk.Canvas(parent, bg=self.colors['secondary'], highlightthickness=0)
        self.shortcuts_scrollbar = ttk.Scrollbar(parent, orient="vertical", command=self.shortcuts_canvas.yview, style='Vertical.TScrollbar')
        self.shortcuts_scrollable_frame = tk.Frame(self.shortcuts_canvas, bg=self.colors['secondary'])

        # Bind configure event to update scroll region when frame content changes
        self.shortcuts_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.shortcuts_canvas.configure(scrollregion=self.shortcuts_canvas.bbox("all"))
        )

        self.shortcuts_canvas.create_window((0, 0), window=self.shortcuts_scrollable_frame, anchor="nw")
        self.shortcuts_canvas.configure(yscrollcommand=self.shortcuts_scrollbar.set)

        self.shortcuts_canvas.pack(side="left", fill="both", expand=True)
        self.shortcuts_scrollbar.pack(side="right", fill="y")

        # Bind mousewheel for scrolling
        self.shortcuts_canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.shortcuts_scrollable_frame.bind("<MouseWheel>", self._on_mousewheel)

        self.refresh_shortcuts_view() # Initial display of shortcuts

    def _on_mousewheel(self, event):
        """Handles mouse wheel scrolling for the canvas."""
        self.shortcuts_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def switch_view_mode(self):
        """No longer needed as only list view is available."""
        pass # This method is kept for compatibility but does nothing now

    def refresh_shortcuts_view(self):
        """Refreshes the display of shortcuts based on current filters (always list view)."""
        # Clear existing widgets in the scrollable frame
        for widget in self.shortcuts_scrollable_frame.winfo_children():
            widget.destroy()

        if not self.shortcuts:
            # Show an empty state message if no shortcuts exist
            empty_frame = tk.Frame(self.shortcuts_scrollable_frame, bg=self.colors['secondary'])
            empty_frame.pack(fill=tk.BOTH, expand=True, pady=50)

            tk.Label(empty_frame, text="📱", font=('Segoe UI', 48),
                     bg=self.colors['secondary'], fg=self.colors['text_secondary']).pack()
            tk.Label(empty_frame, text="No shortcuts yet",
                     font=('Segoe UI', 16, 'bold'),
                     bg=self.colors['secondary'], fg=self.colors['text']).pack(pady=(10, 5))
            tk.Label(empty_frame, text="Create your first shortcut to get started",
                     font=('Segoe UI', 11),
                     bg=self.colors['secondary'], fg=self.colors['text_secondary']).pack()
            self.create_modern_button(empty_frame, "➕ Create Shortcut",
                                      self.show_add_shortcut_dialog,
                                      bg=self.colors['success']).pack(pady=20)
            return

        # Filter shortcuts based on search term and category
        filtered_shortcuts = self.get_filtered_shortcuts()

        # Always render shortcuts in list view
        self.create_list_view(filtered_shortcuts)

    def get_filtered_shortcuts(self):
        """Filters shortcuts based on the current search term and category filter."""
        search_term = self.search_var.get().lower()
        category_filter = self.filter_var.get()

        filtered = {}
        for name, data in self.shortcuts.items():
            # Apply search filter
            if search_term and search_term not in name.lower() and \
               search_term not in data.get('description', '').lower():
                continue

            # Apply category filter
            if category_filter != "All" and data.get('category', '') != category_filter:
                continue

            filtered[name] = data
        return filtered

    def create_grid_view(self, shortcuts):
        """This method is no longer used but kept for reference if needed in future."""
        pass

    def create_list_view(self, shortcuts):
        """Arranges shortcuts in a list layout."""
        for i, (name, data) in enumerate(shortcuts.items()):
            row_frame = tk.Frame(self.shortcuts_scrollable_frame, bg=self.colors['surface'], relief=tk.FLAT)
            row_frame.pack(fill=tk.X, padx=5, pady=2)

            # Shortcut info section
            info_frame = tk.Frame(row_frame, bg=self.colors['surface'])
            info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=15, pady=10)

            # Name and category
            name_frame = tk.Frame(info_frame, bg=self.colors['surface'])
            name_frame.pack(fill=tk.X)
            tk.Label(name_frame, text=name, font=('Segoe UI', 12, 'bold'),
                     bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
            tk.Label(name_frame, text=f"[{data.get('category', 'General')}]",
                     font=('Segoe UI', 9), bg=self.colors['surface'],
                     fg=self.colors['text_secondary']).pack(side=tk.RIGHT)

            # Description and usage stats
            desc_text = data.get('description', 'No description')[:100]
            if len(data.get('description', '')) > 100:
                desc_text += "..."
            tk.Label(info_frame, text=desc_text, font=('Segoe UI', 9),
                     bg=self.colors['surface'], fg=self.colors['text_secondary']).pack(anchor='w')

            usage_text = f"Used {data.get('usage_count', 0)} times"
            if data.get('last_used'):
                usage_text += f" • Last: {data['last_used'].split()[0]}"
            tk.Label(info_frame, text=usage_text, font=('Segoe UI', 8),
                     bg=self.colors['surface'], fg=self.colors['text_secondary']).pack(anchor='w')

            # Action buttons for run, edit, delete
            actions_frame = tk.Frame(row_frame, bg=self.colors['surface'])
            actions_frame.pack(side=tk.RIGHT, padx=15, pady=10)
            self.create_modern_button(actions_frame, "▶️", lambda n=name: self.run_shortcut_by_name(n),
                                      bg=self.colors['success'], width=3).pack(side=tk.RIGHT, padx=2)
            self.create_modern_button(actions_frame, "✏️", lambda n=name: self.edit_shortcut_by_name(n),
                                      bg=self.colors['warning'], width=3).pack(side=tk.RIGHT, padx=2)
            self.create_modern_button(actions_frame, "🗑️", lambda n=name: self.delete_shortcut_by_name(n),
                                      bg=self.colors['danger'], width=3).pack(side=tk.RIGHT, padx=2)

    def create_shortcut_card(self, parent, name, data):
        """This method is no longer used as grid view is removed."""
        pass

    def create_ai_tab(self):
        """Creates the AI Assistant tab with smart suggestions and cleanup actions."""
        ai_frame = tk.Frame(self.notebook, bg=self.colors['secondary'])
        self.notebook.add(ai_frame, text="🧠 AI Assistant")

        ai_frame.grid_rowconfigure(1, weight=1)
        ai_frame.grid_columnconfigure(0, weight=1) # Suggestions panel takes left half
        ai_frame.grid_columnconfigure(1, weight=1) # Actions panel takes right half

        tk.Label(ai_frame, text="AI Assistant",
                 font=('Segoe UI', 16, 'bold'), fg=self.colors['text'],
                 bg=self.colors['secondary']).grid(row=0, column=0, columnspan=2, pady=20, padx=30, sticky='w')

        # Suggestions Panel
        suggestions_container = tk.Frame(ai_frame, bg=self.colors['surface'])
        suggestions_container.grid(row=1, column=0, sticky='nsew', padx=30, pady=(0, 20))
        suggestions_container.grid_rowconfigure(1, weight=1)
        suggestions_container.grid_columnconfigure(0, weight=1)

        tk.Label(suggestions_container, text="Smart Suggestions",
                 font=('Segoe UI', 12, 'bold'), fg=self.colors['text'],
                 bg=self.colors['surface']).grid(row=0, column=0, sticky='w', padx=15, pady=15)

        # Frame to hold individual suggestion entries for adding "Add" buttons
        self.ai_suggestions_display_frame = tk.Frame(suggestions_container, bg=self.colors['background'])
        self.ai_suggestions_display_frame.grid(row=1, column=0, sticky='nsew', padx=15, pady=(0, 15))
        self.ai_suggestions_display_frame.grid_columnconfigure(0, weight=1) # Make sure content expands

        # Add a scrollbar to the display frame
        ai_suggestions_canvas = tk.Canvas(self.ai_suggestions_display_frame, bg=self.colors['background'], highlightthickness=0)
        ai_suggestions_scrollbar = ttk.Scrollbar(self.ai_suggestions_display_frame, orient="vertical", command=ai_suggestions_canvas.yview, style='Vertical.TScrollbar')
        self.ai_suggestions_scrollable_content = tk.Frame(ai_suggestions_canvas, bg=self.colors['background'])

        self.ai_suggestions_scrollable_content.bind(
            "<Configure>",
            lambda e: ai_suggestions_canvas.configure(scrollregion=ai_suggestions_canvas.bbox("all"))
        )

        ai_suggestions_canvas.create_window((0, 0), window=self.ai_suggestions_scrollable_content, anchor="nw")
        ai_suggestions_canvas.configure(yscrollcommand=ai_suggestions_scrollbar.set)

        ai_suggestions_canvas.pack(side="left", fill="both", expand=True)
        ai_suggestions_scrollbar.pack(side="right", fill="y")
        ai_suggestions_canvas.bind("<MouseWheel>", self._on_ai_suggestions_mousewheel)
        self.ai_suggestions_scrollable_content.bind("<MouseWheel>", self._on_ai_suggestions_mousewheel)

        self.create_modern_button(suggestions_container, "Generate Suggestions", self.generate_ai_suggestions,
                                  bg=self.colors['accent']).grid(row=2, column=0, sticky='ew', padx=15, pady=(0,15))

        # AI Actions Panel
        actions_frame = tk.Frame(ai_frame, bg=self.colors['surface'])
        actions_frame.grid(row=1, column=1, sticky='nsew', padx=30, pady=(0, 20))
        actions_frame.grid_rowconfigure(0, weight=0) # Title row
        actions_frame.grid_columnconfigure(0, weight=1)

        tk.Label(actions_frame, text="AI Actions",
                 font=('Segoe UI', 12, 'bold'), fg=self.colors['text'],
                 bg=self.colors['surface']).pack(anchor='w', padx=15, pady=15)

        self.create_modern_button(actions_frame, "Scan for Redundant Shortcuts", self.scan_redundant_shortcuts,
                                  bg=self.colors['warning']).pack(fill=tk.X, padx=15, pady=5)
        
        self.create_modern_button(actions_frame, "Remove Broken Shortcuts", self.remove_broken_shortcuts,
                                  bg=self.colors['danger']).pack(fill=tk.X, padx=15, pady=5)

        self.create_modern_button(actions_frame, "Remove Unused Shortcuts", self.remove_unused_shortcuts,
                                  bg=self.colors['danger']).pack(fill=tk.X, padx=15, pady=5)
        
        self.ai_action_status_label = tk.Label(actions_frame, text="No issues found.",
                                                font=('Segoe UI', 9), fg=self.colors['text_secondary'],
                                                bg=self.colors['surface'], wraplength=250, justify=tk.LEFT)
        self.ai_action_status_label.pack(padx=15, pady=10, anchor='w')

        self.generate_ai_suggestions() # Initial suggestions on tab load

    def _on_ai_suggestions_mousewheel(self, event):
        """Handles mouse wheel scrolling for the AI suggestions canvas."""
        self.ai_suggestions_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def generate_ai_suggestions(self, update_dashboard=False):
        """Generates simulated AI suggestions based on common patterns and usage.
           Can also update the dashboard AI recommendations if update_dashboard is True.
           This method is enhanced to provide more "AI-like" suggestions.
        """
        # Clear existing suggestions from both UI elements
        for widget in self.ai_suggestions_scrollable_content.winfo_children():
            widget.destroy()
        self.dashboard_ai_suggestions_text.delete("1.0", tk.END)
        # Clear existing buttons on dashboard AI recommendations
        for widget in self.dashboard_ai_add_buttons_frame.winfo_children():
            widget.destroy()


        # Display loading messages
        loading_label = tk.Label(self.ai_suggestions_scrollable_content, text="Analyzing your shortcuts and system...",
                                 font=('Segoe UI', 10), fg=self.colors['text_secondary'], bg=self.colors['background'])
        loading_label.pack(pady=20)
        self.dashboard_ai_suggestions_text.insert("1.0", "Analyzing...\n")
        
        self.root.update_idletasks() # Refresh UI to show loading message

        suggestions_data = [] # List to hold structured suggestion data
        dashboard_suggestions_summary = []
        dashboard_add_buttons_info = [] # To store info for dashboard buttons

        # Suggest common applications if not already added
        common_apps = {
            "Notepad": {"target": "notepad.exe", "category": "Application", "description": "Windows Notepad text editor."},
            "Calculator": {"target": "calc.exe", "category": "Application", "description": "Windows Calculator."},
            "Command Prompt": {"target": "cmd.exe", "category": "Application", "description": "Windows Command Prompt."},
            "File Explorer": {"target": "explorer.exe", "category": "Application", "description": "Open Windows File Explorer."},
            "Control Panel": {"target": "control.exe", "category": "Application", "description": "Open Windows Control Panel."},
            "Google Chrome": {"target": "https://www.google.com/chrome", "category": "Website", "description": "Google Chrome web browser. (Default path, may vary)"}, # Suggest browser if not present
            "Mozilla Firefox": {"target": "https://www.mozilla.org/firefox", "category": "Website", "description": "Mozilla Firefox web browser. (Default path, may vary)"},
            "Microsoft Edge": {"target": "https://www.microsoft.com/edge", "category": "Website", "description": "Microsoft Edge web browser. (Default path, may vary)"},
            "PowerShell": {"target": "powershell.exe", "category": "Application", "description": "Windows PowerShell."},
            "Paint": {"target": "mspaint.exe", "category": "Application", "description": "Microsoft Paint."},
            "Task Manager": {"target": "taskmgr.exe", "category": "Application", "description": "Open Task Manager."},
            "WordPad": {"target": "wordpad.exe", "category": "Application", "description": "Windows WordPad rich text editor."},
            "Registry Editor": {"target": "regedit.exe", "category": "Application", "description": "Open Windows Registry Editor."},
            "Disk Management": {"target": "diskmgmt.msc", "category": "System", "description": "Open Disk Management console."},
            "Services": {"target": "services.msc", "category": "System", "description": "Open Windows Services console."},
            "Device Manager": {"target": "devmgmt.msc", "category": "System", "description": "Open Windows Device Manager."}
        }
        existing_targets = {data['target'].lower() for data in self.shortcuts.values()}
        for name, info in common_apps.items():
            if info['target'].lower() not in existing_targets:
                suggestions_data.append({
                    "type": "add",
                    "name": name,
                    "target": info['target'],
                    "category": info['category'],
                    "description": info['description']
                })
                dashboard_suggestions_summary.append(f"- Add {name}")
                dashboard_add_buttons_info.append({"name": name, "target": info['target'], "category": info['category'], "description": info['description']})

        # Suggest popular websites if not added
        common_websites = {
            "Google Search": "https://www.google.com", 
            "YouTube": "https://www.youtube.com",
            "Wikipedia": "https://www.wikipedia.org", 
            "GitHub": "https://github.com",
            "Stack Overflow": "https://stackoverflow.com"
        }
        for name, url in common_websites.items():
            if url.lower() not in existing_targets:
                suggestions_data.append({
                    "type": "add",
                    "name": name,
                    "target": url,
                    "category": "Website",
                    "description": f"Access {name} quickly."
                })
                dashboard_suggestions_summary.append(f"- Add {name}")
                dashboard_add_buttons_info.append({"name": name, "target": url, "category": "Website", "description": f"Access {name} quickly."})

        # Suggest cleanup actions
        unused_threshold_days = 180 # 6 months
        unused_shortcuts = []
        now = datetime.now()
        for name, data in self.shortcuts.items():
            last_used_str = data.get('last_used')
            usage_count = data.get('usage_count', 0)
            if last_used_str:
                last_used_dt = datetime.strptime(last_used_str, '%Y-%m-%d %H:%M:%S')
                if (now - last_used_dt).days > unused_threshold_days and usage_count <= 5: # Low usage and old
                    unused_shortcuts.append(name)
            elif usage_count == 0: # Never used
                unused_shortcuts.append(name)
        
        if unused_shortcuts:
            suggestions_data.append({
                "type": "cleanup_message",
                "message": f"You have {len(unused_shortcuts)} shortcuts that appear to be unused or rarely used.",
                "action": "Remove Unused Shortcuts"
            })
            dashboard_suggestions_summary.append(f"- Clean up {len(unused_shortcuts)} unused shortcuts")

        # Suggest organizing 'General' category shortcuts
        general_shortcuts = [name for name, data in self.shortcuts.items() if data.get('category') == 'General']
        if len(general_shortcuts) > 5: # If many are in General
            suggestions_data.append({
                "type": "organize_message",
                "message": f"You have {len(general_shortcuts)} shortcuts in the 'General' category. Consider assigning them to more specific categories (e.g., Application, Website) for better organization.",
                "examples": general_shortcuts[:5]
            })
            dashboard_suggestions_summary.append(f"- Organize {len(general_shortcuts)} 'General' shortcuts")

        # Clear the loading label
        loading_label.destroy()

        if not suggestions_data:
            tk.Label(self.ai_suggestions_scrollable_content, text="No new suggestions at this time. Keep using the app to help AI learn more!",
                     font=('Segoe UI', 10), fg=self.colors['text'], bg=self.colors['background'], wraplength=400, justify=tk.LEFT).pack(pady=20, padx=10)
            if update_dashboard:
                self.dashboard_ai_suggestions_text.insert(tk.END, "No new suggestions at this time.\n")
        else:
            self.display_ai_suggestions(suggestions_data)
            if update_dashboard:
                dashboard_summary_text = "\n".join(dashboard_suggestions_summary[:5]) # Show top 5 summary points
                if len(dashboard_suggestions_summary) > 5:
                    dashboard_summary_text += "\n... (more in AI Assistant tab)"
                self.dashboard_ai_suggestions_text.insert(tk.END, dashboard_summary_text)
                
                # Add "Add" buttons to dashboard for top 3 suggestions
                for i, info in enumerate(dashboard_add_buttons_info[:3]):
                    self.create_modern_button(self.dashboard_ai_add_buttons_frame, f"Add {info['name']}",
                                              lambda n=info['name'], t=info['target'], c=info['category'], d=info['description']: self.add_ai_suggested_shortcut(n, t, c, d),
                                              bg=self.colors['success'], width=15).pack(side=tk.LEFT, padx=5, pady=5)


        self.update_status("AI suggestions generated.")
        self.root.update_idletasks() # Ensure UI is updated

    def display_ai_suggestions(self, suggestions_data):
        """Displays structured AI suggestions with 'Add' buttons."""
        for i, suggestion in enumerate(suggestions_data):
            suggestion_frame = tk.Frame(self.ai_suggestions_scrollable_content, bg=self.colors['surface'], padx=10, pady=8)
            suggestion_frame.pack(fill=tk.X, pady=2, padx=5)
            suggestion_frame.grid_columnconfigure(0, weight=1) # Text column

            if suggestion['type'] == "add":
                name = suggestion['name']
                target = suggestion['target']
                category = suggestion['category']
                description = suggestion['description']

                tk.Label(suggestion_frame, text=f"• Consider adding a shortcut for **{name}** (Target: {target}). Category: {category}",
                         font=('Segoe UI', 9), fg=self.colors['text'], bg=self.colors['surface'],
                         wraplength=350, justify=tk.LEFT).grid(row=0, column=0, sticky='w', pady=2)
                
                # Add button
                self.create_modern_button(suggestion_frame, "Add", 
                                          lambda n=name, t=target, c=category, d=description: self.add_ai_suggested_shortcut(n, t, c, d),
                                          bg=self.colors['success'], width=6).grid(row=0, column=1, padx=5, sticky='e')
                
            elif suggestion['type'] == "cleanup_message":
                tk.Label(suggestion_frame, text=f"• {suggestion['message']}\n  (Use '{suggestion['action']}' button below to take action.)",
                         font=('Segoe UI', 9), fg=self.colors['warning'], bg=self.colors['surface'],
                         wraplength=450, justify=tk.LEFT).grid(row=0, column=0, columnspan=2, sticky='w', pady=2)
            
            elif suggestion['type'] == "organize_message":
                examples_text = f"Examples: {', '.join(suggestion['examples'])}{'...' if len(suggestion['examples']) > 5 else ''}"
                tk.Label(suggestion_frame, text=f"• {suggestion['message']}\n  {examples_text}",
                         font=('Segoe UI', 9), fg=self.colors['text_secondary'], bg=self.colors['surface'],
                         wraplength=450, justify=tk.LEFT).grid(row=0, column=0, columnspan=2, sticky='w', pady=2)


    def add_ai_suggested_shortcut(self, name, target, category, description):
        """Adds a shortcut suggested by AI Assistant, confirming with the user."""
        if name in self.shortcuts:
            messagebox.showinfo("Shortcut Exists", f"A shortcut for '{name}' already exists.")
            return

        response = messagebox.askyesno("Confirm Add Shortcut",
                                       f"Add '{name}' as a shortcut?\n"
                                       f"Target: {target}\n"
                                       f"Category: {category}\n"
                                       f"Description: {description}")
        if response:
            success = self.add_shortcut(name, target, category, description, hotkey="") # No hotkey by default
            if success:
                messagebox.showinfo("Shortcut Added", f"'{name}' added successfully!")
                self.generate_ai_suggestions(update_dashboard=True) # Regenerate suggestions to remove the just-added one and update dashboard
            else:
                messagebox.showerror("Add Error", f"Failed to add '{name}'. It might already exist or there was another issue.")

    def scan_redundant_shortcuts(self):
        """Scans for redundant shortcuts (same target, different names) and reports them."""
        redundant_found = False
        target_to_names = collections.defaultdict(list)
        for name, data in self.shortcuts.items():
            target_to_names[data['target'].lower()].append(name)

        redundant_messages = []
        for target, names in target_to_names.items():
            if len(names) > 1:
                redundant_messages.append(f"- Multiple shortcuts point to the same target: '{target}'. Names: {', '.join(names)}")
                redundant_found = True

        if redundant_found:
            msg = "Redundant shortcuts found:\n" + "\n".join(redundant_messages)
            self.ai_action_status_label.config(text=msg, fg=self.colors['warning'])
            messagebox.showwarning("Redundant Shortcuts", msg)
        else:
            self.ai_action_status_label.config(text="No redundant shortcuts found.", fg=self.colors['text_secondary'])
            messagebox.showinfo("Redundant Shortcuts", "No redundant shortcuts found.")
        self.update_status("Scanned for redundant shortcuts.")

    def remove_broken_shortcuts(self):
        """Removes shortcuts pointing to non-existent files/folders/apps."""
        broken_shortcuts = []
        shortcuts_to_remove = []

        for name, data in self.shortcuts.items():
            target = data['target']
            category = data['category']

            is_broken = False
            if category in ['Application', 'File', 'Folder']:
                if not Path(target).exists():
                    is_broken = True
            # For websites, we can't easily check validity without making a request
            # For system commands, it's hard to validate generically without running them

            if is_broken:
                broken_shortcuts.append(name)
                shortcuts_to_remove.append(name)
        
        if broken_shortcuts:
            response = messagebox.askyesno("Remove Broken Shortcuts",
                                           f"Found {len(broken_shortcuts)} broken shortcuts:\n" +
                                           "\n".join(broken_shortcuts) +
                                           "\n\nDo you want to remove them?")
            if response:
                for name in shortcuts_to_remove:
                    if name in self.shortcuts: # Double check before deleting
                        del self.shortcuts[name]
                self.save_shortcuts()
                self.refresh_shortcuts_view()
                self.update_dashboard_stats()
                self.refresh_analytics_data()
                self.ai_action_status_label.config(text=f"Removed {len(broken_shortcuts)} broken shortcuts.", fg=self.colors['success'])
                messagebox.showinfo("Removed Broken Shortcuts", f"Successfully removed {len(broken_shortcuts)} broken shortcuts.")
                self.generate_ai_suggestions(update_dashboard=True) # Refresh suggestions after cleanup
            else:
                self.ai_action_status_label.config(text="Broken shortcut removal cancelled.", fg=self.colors['text_secondary'])
        else:
            self.ai_action_status_label.config(text="No broken shortcuts found.", fg=self.colors['text_secondary'])
            messagebox.showinfo("Broken Shortcuts", "No broken shortcuts found.")
        self.update_status("Scanned and acted on broken shortcuts.")

    def remove_unused_shortcuts(self):
        """Removes shortcuts that haven't been used for a long time or have very low usage."""
        unused_threshold_days = 180 # 6 months
        unused_shortcuts_names = []
        shortcuts_to_remove = []
        now = datetime.now()

        for name, data in self.shortcuts.items():
            last_used_str = data.get('last_used')
            usage_count = data.get('usage_count', 0)

            is_unused = False
            if last_used_str:
                last_used_dt = datetime.strptime(last_used_str, '%Y-%m-%d %H:%M:%S')
                if (now - last_used_dt).days > unused_threshold_days and usage_count <= 5:
                    is_unused = True
            elif usage_count == 0: # Never used
                is_unused = True
            
            if is_unused:
                unused_shortcuts_names.append(name)
                shortcuts_to_remove.append(name)
        
        if unused_shortcuts_names:
            response = messagebox.askyesno("Remove Unused Shortcuts",
                                           f"Found {len(unused_shortcuts_names)} unused or rarely used shortcuts:\n" +
                                           "\n".join(unused_shortcuts_names) +
                                           "\n\nDo you want to remove them?")
            if response:
                for name in shortcuts_to_remove:
                    if name in self.shortcuts:
                        del self.shortcuts[name]
                self.save_shortcuts()
                self.refresh_shortcuts_view()
                self.update_dashboard_stats()
                self.refresh_analytics_data()
                self.ai_action_status_label.config(text=f"Removed {len(unused_shortcuts_names)} unused shortcuts.", fg=self.colors['success'])
                messagebox.showinfo("Removed Unused Shortcuts", f"Successfully removed {len(unused_shortcuts_names)} unused shortcuts.")
                self.generate_ai_suggestions(update_dashboard=True) # Refresh suggestions after cleanup
            else:
                self.ai_action_status_label.config(text="Unused shortcut removal cancelled.", fg=self.colors['text_secondary'])
        else:
            self.ai_action_status_label.config(text="No unused shortcuts found based on criteria.", fg=self.colors['text_secondary'])
            messagebox.showinfo("Unused Shortcuts", "No unused shortcuts found based on current criteria.")
        self.update_status("Scanned and acted on unused shortcuts.")


    def create_analytics_tab(self):
        """Creates the Analytics tab with usage statistics and trends."""
        analytics_frame = tk.Frame(self.notebook, bg=self.colors['secondary'])
        self.notebook.add(analytics_frame, text="📈 Analytics")

        analytics_frame.grid_rowconfigure(1, weight=1)
        analytics_frame.grid_columnconfigure(0, weight=1)
        analytics_frame.grid_columnconfigure(1, weight=1)

        tk.Label(analytics_frame, text="Usage Analytics",
                 font=('Segoe UI', 16, 'bold'), fg=self.colors['text'],
                 bg=self.colors['secondary']).grid(row=0, column=0, columnspan=2, pady=20, padx=30, sticky='w')

        # Usage Overview Panel
        overview_frame = tk.Frame(analytics_frame, bg=self.colors['surface'], padx=20, pady=20)
        overview_frame.grid(row=1, column=0, sticky='nsew', padx=30, pady=(0, 20))
        overview_frame.grid_rowconfigure(0, weight=0)
        overview_frame.grid_columnconfigure(0, weight=1)

        tk.Label(overview_frame, text="Overview",
                 font=('Segoe UI', 12, 'bold'), fg=self.colors['text'],
                 bg=self.colors['surface']).pack(anchor='w', pady=(0, 10))

        self.analytics_total_shortcuts = tk.Label(overview_frame, text="Total Shortcuts: 0",
                                                  font=('Segoe UI', 10), fg=self.colors['text_secondary'],
                                                  bg=self.colors['surface'], anchor='w')
        self.analytics_total_shortcuts.pack(fill=tk.X)

        self.analytics_total_launches = tk.Label(overview_frame, text="Total Launches: 0",
                                                 font=('Segoe UI', 10), fg=self.colors['text_secondary'],
                                                 bg=self.colors['surface'], anchor='w')
        self.analytics_total_launches.pack(fill=tk.X)

        self.analytics_categories = tk.Label(overview_frame, text="Categories: 0 unique",
                                            font=('Segoe UI', 10), fg=self.colors['text_secondary'],
                                            bg=self.colors['surface'], anchor='w')
        self.analytics_categories.pack(fill=tk.X)

        tk.Label(overview_frame, text="\nTop 5 Most Used:",
                 font=('Segoe UI', 10, 'bold'), fg=self.colors['text'],
                 bg=self.colors['surface'], anchor='w').pack(fill=tk.X)
        self.analytics_most_used_list = tk.Listbox(overview_frame, bg=self.colors['background'],
                                                   fg=self.colors['text'], font=('Segoe UI', 9),
                                                   relief=tk.FLAT, height=5)
        self.analytics_most_used_list.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # Usage History / Trends Panel
        trends_frame = tk.Frame(analytics_frame, bg=self.colors['surface'], padx=20, pady=20)
        trends_frame.grid(row=1, column=1, sticky='nsew', padx=30, pady=(0, 20))
        trends_frame.grid_rowconfigure(0, weight=0)
        trends_frame.grid_columnconfigure(0, weight=1)

        tk.Label(trends_frame, text="Usage Trends (Daily Launches)",
                 font=('Segoe UI', 12, 'bold'), fg=self.colors['text'],
                 bg=self.colors['surface']).pack(anchor='w', pady=(0, 10))
        
        self.analytics_trend_text = tk.Text(trends_frame, wrap=tk.WORD, bg=self.colors['background'],
                                            fg=self.colors['text'], font=('Consolas', 8), relief=tk.FLAT,
                                            insertbackground=self.colors['text'], height=10)
        self.analytics_trend_text.pack(fill=tk.BOTH, expand=True)

        self.refresh_analytics_data() # Initial data load

    def refresh_analytics_data(self):
        """Updates the analytics data displayed in the analytics tab."""
        # Overview stats
        total_shortcuts = len(self.shortcuts)
        total_launches = sum(data.get('usage_count', 0) for data in self.shortcuts.values())
        categories = set(data.get('category', 'General') for data in self.shortcuts.values())

        self.analytics_total_shortcuts.config(text=f"Total Shortcuts: {total_shortcuts}")
        self.analytics_total_launches.config(text=f"Total Launches: {total_launches}")
        self.analytics_categories.config(text=f"Categories: {len(categories)} unique")

        # Most Used Shortcuts list
        self.analytics_most_used_list.delete(0, tk.END)
        sorted_by_usage = sorted(
            self.shortcuts.items(),
            key=lambda item: item[1].get('usage_count', 0),
            reverse=True
        )
        for i, (name, data) in enumerate(sorted_by_usage[:5]):
            self.analytics_most_used_list.insert(tk.END, f"{i+1}. {name}: {data.get('usage_count', 0)} uses")

        # Usage Trends (Simplified Text Chart)
        self.analytics_trend_text.delete("1.0", tk.END)
        if not self.usage_stats:
            self.analytics_trend_text.insert("1.0", "No historical usage data yet. Start launching shortcuts to see trends!\n")
            return

        # Prepare daily launch data for chart
        daily_launches = collections.defaultdict(int)
        for date_str, count in self.usage_stats.items():
            daily_launches[date_str] = count
        
        # Get last 7 days for the chart
        today = datetime.now()
        dates_to_chart = [(today - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(6, -1, -1)] # Last 7 days, oldest first

        chart_data_lines = []
        all_counts = [daily_launches.get(d, 0) for d in dates_to_chart]
        max_launches_day = max(all_counts) if all_counts else 1 # Avoid division by zero

        chart_data_lines.append("Date        Launches | Chart\n")
        chart_data_lines.append("-----------------------------\n")
        for date_str in dates_to_chart:
            count = daily_launches.get(date_str, 0)
            # Scale bar length to max 20 blocks
            bar_length = int((count / max_launches_day) * 20)
            bar = "█" * bar_length
            chart_data_lines.append(f"{date_str} {count:7} | {bar}\n")

        self.analytics_trend_text.insert("1.0", "".join(chart_data_lines))
        self.update_status("Analytics data refreshed.")

    def create_settings_tab(self):
        """Creates the Settings tab for application configuration."""
        settings_frame = tk.Frame(self.notebook, bg=self.colors['secondary'])
        self.notebook.add(settings_frame, text="⚙️ Settings")

        tk.Label(settings_frame, text="Application Settings",
                 font=('Segoe UI', 16, 'bold'), fg=self.colors['text'],
                 bg=self.colors['secondary']).pack(pady=20, anchor='w', padx=30)

        # Theme setting
        theme_frame = tk.Frame(settings_frame, bg=self.colors['surface'], padx=20, pady=15)
        theme_frame.pack(fill=tk.X, padx=30, pady=10)
        tk.Label(theme_frame, text="Theme:", font=('Segoe UI', 10), bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
        self.theme_var = tk.StringVar(value=self.settings.get('theme', 'dark'))
        # Only Dark theme option is shown
        ttk.Radiobutton(theme_frame, text="Dark", variable=self.theme_var, value="dark", command=self.apply_theme_settings).pack(side=tk.LEFT, padx=10)

        # Auto-save setting
        autosave_frame = tk.Frame(settings_frame, bg=self.colors['surface'], padx=20, pady=15)
        autosave_frame.pack(fill=tk.X, padx=30, pady=10)
        self.autosave_var = tk.BooleanVar(value=self.settings.get('auto_save', True))
        tk.Checkbutton(autosave_frame, text="Enable Auto-Save", variable=self.autosave_var,
                       bg=self.colors['surface'], fg=self.colors['text'], selectcolor=self.colors['surface'],
                       command=self.save_settings).pack(anchor='w')

        # Backup setting
        backup_frame = tk.Frame(settings_frame, bg=self.colors['surface'], padx=20, pady=15)
        backup_frame.pack(fill=tk.X, padx=30, pady=10)
        self.backup_var = tk.BooleanVar(value=self.settings.get('backup_enabled', True))
        tk.Checkbutton(backup_frame, text="Enable Automatic Backups (last 5)", variable=self.backup_var,
                       bg=self.colors['surface'], fg=self.colors['text'], selectcolor=self.colors['surface'],
                       command=self.save_settings).pack(anchor='w')

        # Hotkeys setting
        hotkeys_frame = tk.Frame(settings_frame, bg=self.colors['surface'], padx=20, pady=15)
        hotkeys_frame.pack(fill=tk.X, padx=30, pady=10)
        self.hotkeys_var = tk.BooleanVar(value=self.settings.get('hotkeys_enabled', True))
        hotkey_checkbox = tk.Checkbutton(hotkeys_frame, text="Enable Global Hotkeys", variable=self.hotkeys_var,
                                         bg=self.colors['surface'], fg=self.colors['text'], selectcolor=self.colors['surface'],
                                         command=self.toggle_global_hotkeys)
        hotkey_checkbox.pack(anchor='w')
        if not GLOBAL_HOTKEYS_AVAILABLE:
            hotkey_checkbox.config(state=tk.DISABLED, text="Enable Global Hotkeys")
            tk.Label(hotkeys_frame, text=" ",
                     font=('Segoe UI', 8, 'italic'), fg=self.colors['danger'], bg=self.colors['surface']).pack(anchor='w', padx=20)


    def apply_theme_settings(self):
        """Applies the selected theme settings."""
        new_theme = self.theme_var.get()
        if new_theme == 'dark':
            # Currently, the app is designed for dark theme. No major changes needed here.
            pass
        # Removed the 'light' theme handling as per request
        self.settings['theme'] = self.theme_var.get()
        self.save_settings()
        self.refresh_all_data() # Refresh UI to apply potential style changes

    def toggle_global_hotkeys(self):
        """Enables or disables the global hotkey listener."""
        self.save_settings() # Save the hotkeys_enabled setting
        if not GLOBAL_HOTKEYS_AVAILABLE:
            messagebox.showwarning("Hotkeys Disabled", "The 'keyboard' library is not installed. Global hotkeys cannot be enabled.")
            self.hotkeys_var.set(False) # Ensure checkbox is unchecked
            return

        if self.hotkeys_var.get():
            if not self.hotkey_running:
                self._start_hotkey_listener()
                self.update_status("Global hotkeys enabled.")
            else:
                self.update_status("Global hotkeys already running.")
        else:
            if self.hotkey_running:
                self._stop_hotkey_listener()
                self.update_status("Global hotkeys disabled.")
            else:
                self.update_status("Global hotkeys already stopped.")
        self.refresh_all_data() # Refresh to update hotkey status on cards

    def _start_hotkey_listener(self):
        """Starts the global hotkey listener in a separate thread."""
        if not GLOBAL_HOTKEYS_AVAILABLE:
            return

        self._stop_hotkey_listener() # Ensure any old listener is stopped first
        
        self.hotkey_running = True
        self.registered_hotkeys = {} # Clear previously registered hotkeys

        def listener_target():
            try:
                # Unhook all existing hotkeys before registering new ones
                keyboard.unhook_all()
                for name, data in self.shortcuts.items():
                    hotkey = data.get('hotkey')
                    if hotkey:
                        try:
                            # Register hotkey to call a wrapper function
                            # Use a lambda to pass the shortcut name
                            keyboard.add_hotkey(hotkey, lambda n=name: self.root.after(0, self.run_shortcut_by_name, n))
                            self.registered_hotkeys[name] = hotkey # Store for unhooking
                            print(f"Registered hotkey: {hotkey} for {name}")
                        except Exception as e:
                            print(f"Error registering hotkey '{hotkey}' for '{name}': {e}")
                            # messagebox.showwarning("Hotkey Error", f"Could not register hotkey '{hotkey}' for '{name}': {e}")
                keyboard.wait() # Keep the thread alive
            except Exception as e:
                print(f"Hotkey listener error: {e}")
                self.root.after(0, messagebox.showerror, "Hotkey Listener Error", f"An error occurred in the hotkey listener: {e}")
                self.hotkey_running = False # Mark as stopped on error

        self.hotkey_listener_thread = threading.Thread(target=listener_target, daemon=True)
        self.hotkey_listener_thread.start()
        self.update_status("Global hotkey listener started.")

    def _stop_hotkey_listener(self):
        """Stops the global hotkey listener."""
        if not GLOBAL_HOTKEYS_AVAILABLE:
            return

        if self.hotkey_running:
            try:
                keyboard.unhook_all() # Unhook all registered hotkeys
                self.hotkey_running = False
                self.registered_hotkeys = {}
                print("Global hotkey listener stopped.")
            except Exception as e:
                print(f"Error stopping hotkey listener: {e}")
                messagebox.showerror("Hotkey Error", f"Error stopping hotkey listener: {e}")
        self.update_status("Global hotkey listener stopped.")


    def create_help_tab(self):
        """Creates a dedicated help/how-to tab with multilingual support."""
        help_frame = tk.Frame(self.notebook, bg=self.colors['secondary'])
        self.notebook.add(help_frame, text="❓ Help")

        help_frame.grid_rowconfigure(1, weight=1)
        help_frame.grid_columnconfigure(0, weight=1)

        # Language selection
        lang_frame = tk.Frame(help_frame, bg=self.colors['surface'], padx=20, pady=15)
        lang_frame.grid(row=0, column=0, sticky='ew', padx=30, pady=(20, 10))

        tk.Label(lang_frame, text="Select Language:", font=('Segoe UI', 10),
                 bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.help_lang_var = tk.StringVar(value=self.settings.get('help_language', 'English'))
        self.help_lang_combo = ttk.Combobox(lang_frame, textvariable=self.help_lang_var,
                                            values=["English", "Hindi", "Marathi"],
                                            state="readonly", style='TCombobox')
        self.help_lang_combo.pack(side=tk.LEFT, padx=10)
        self.help_lang_combo.bind('<<ComboboxSelected>>', self.update_help_content)

        # Help content area
        help_content_frame = tk.Frame(help_frame, bg=self.colors['surface'], padx=20, pady=20)
        help_content_frame.grid(row=1, column=0, sticky='nsew', padx=30, pady=(0, 20))

        self.help_text = tk.Text(help_content_frame, wrap=tk.WORD, bg=self.colors['background'],
                                 fg=self.colors['text'], font=('Segoe UI', 10), relief=tk.FLAT,
                                 insertbackground=self.colors['text'])
        self.help_text.pack(fill=tk.BOTH, expand=True)

        # Add a scrollbar to the text widget
        help_scrollbar = ttk.Scrollbar(help_content_frame, command=self.help_text.yview, style='Vertical.TScrollbar')
        help_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.help_text.config(yscrollcommand=help_scrollbar.set)

        self.help_content = {
            "English": """
            Welcome to AI Shortcuts Manager Pro!

            This application helps you organize and quickly access your frequently used applications, websites, files, and folders.

            --- How to Use This App ---

            1.  **Dashboard (📊):**
                * Get an overview of your shortcuts, including total count, most used, categories, and total launches.
                * See your recent shortcut activity.
                * Use "Quick Actions" to instantly add a new shortcut or generate a usage report.

            2.  **Shortcuts (⚡):**
                * **Adding a Shortcut:**
                    * Click the "➕ Add" button (or "⚡ Quick Add" on Dashboard).
                    * Fill in the "Shortcut Name", "Target Path/URL", and optionally a "Hotkey".
                    * Use "Browse File" or "Browse Folder" to easily select paths.
                    * Choose a "Category" (Application, Website, File, Folder, System).
                    * Add a "Description" for clarity.
                    * Click "Add Shortcut".
                * **Running a Shortcut:**
                    * In List View: Click the "▶️" button next to the shortcut.
                    * **Using Hotkey:** If a global hotkey is assigned and enabled in settings, simply press the hotkey combination (e.g., `Ctrl+Alt+S`).
                * **Editing a Shortcut:**
                    * Click the "✏️" button next to the shortcut (List View).
                    * Modify details in the dialog and click "Save Changes".
                * **Deleting a Shortcut:**
                    * Click the "🗑️" button next to the shortcut (List View).
                    * Confirm deletion in the prompt.
                * **Searching and Filtering:**
                    * Use the search bar to find shortcuts by name or description.
                    * Use the "Category" dropdown to filter shortcuts by type.
                * **Import/Export:**
                    * "📤 Export": Save all your shortcut data to a JSON file (useful for backup or migration).
                    * "📥 Import": Load shortcut data from a JSON file. Be careful, this will merge with existing data.

            3.  **AI Assistant (🧠):**
                * This tab provides intelligent suggestions for new shortcuts, identifies redundant ones, and helps clean up broken/unused links.
                * **Generate Suggestions:** Get new ideas for shortcuts based on common applications/websites, and suggestions to clean up old/unused shortcuts.
                * **Add Button:** Click 'Add' next to a suggestion to automatically create that shortcut.
                * **Scan for Redundant Shortcuts:** Checks if you have multiple shortcuts pointing to the same target, helping you organize.
                * **Remove Broken Shortcuts:** Automatically deletes shortcuts that no longer lead to valid files, folders, or applications.
                * **Remove Unused Shortcuts:** Identifies and offers to remove shortcuts that haven't been used for a long time (e.g., 6 months) or have very low usage.

            4.  **Analytics (📈):**
                * This tab offers detailed usage statistics, including total launches and category distribution.
                * See your "Top 5 Most Used" shortcuts.
                * "Usage Trends" provides a visual (text-based) representation of your daily shortcut launches over the last 7 days.

            5.  **Settings (⚙️):**
                * **Theme:** Change the application's visual theme (currently only Dark is fully supported).
                * **Auto-Save:** Toggle automatic saving of your shortcut data.
                * **Automatic Backups:** Enable/disable automatic backups of your shortcut data (keeps last 5 versions).
                * **Global Hotkeys:** Enable/disable global hotkeys for quick access. (Requires 'keyboard' Python library: `pip install keyboard`).

            --- Tips & Tricks ---
            * Keep your shortcut names descriptive and easy to remember.
            * Regularly back up your data using the Export feature.
            * For system commands, enter the exact command string in the Target field (e.g., `notepad.exe` for Notepad, or specific shell commands like `shutdown /s /t 0` for Windows shutdown).
            * Ensure the target path/URL is correct for the shortcut to function properly.
            * For hotkeys, use standard combinations like `ctrl+alt+a`, `shift+f1`, etc. Avoid common system hotkeys to prevent conflicts.

            Enjoy streamlining your workflow with AI Shortcuts Manager Pro!
            """,
            "Hindi": """
            एआई शॉर्टकट्स मैनेजर प्रो में आपका स्वागत है!

            यह एप्लिकेशन आपके अक्सर उपयोग किए जाने वाले एप्लिकेशन, वेबसाइट, फ़ाइलों और फ़ोल्डरों को व्यवस्थित करने और तुरंत एक्सेस करने में आपकी मदद करता है।

            --- इस ऐप का उपयोग कैसे करें ---

            1.  **डैशबोर्ड (📊):**
                * अपने शॉर्टकट्स का एक अवलोकन प्राप्त करें, जिसमें कुल संख्या, सबसे अधिक उपयोग किए गए, श्रेणियाँ और कुल लॉन्च शामिल हैं।
                * अपनी हाल की शॉर्टकट गतिविधि देखें।
                * एक नया शॉर्टकट तुरंत जोड़ने या उपयोग रिपोर्ट बनाने के लिए "त्वरित क्रियाएँ" (Quick Actions) का उपयोग करें।

            2.  **शॉर्टकट्स (⚡):**
                * **एक शॉर्टकट जोड़ना:**
                    * "➕ जोड़ें" बटन पर क्लिक करें (या डैशबोर्ड पर "⚡ त्वरित जोड़ें")।
                    * "शॉर्टकट नाम", "लक्ष्य पथ/यूआरएल", और वैकल्पिक रूप से एक "हॉटकी" भरें।
                    * आसानी से पथ चुनने के लिए "फाइल ब्राउज़ करें" (Browse File) या "फ़ोल्डर ब्राउज़ करें" (Folder Browse) का उपयोग करें।
                    * एक "श्रेणी" (एप्लिकेशन, वेबसाइट, फ़ाइल, फ़ोल्डर, सिस्टम) चुनें।
                    * स्पष्टता के लिए एक "विवरण" जोड़ें।
                    * "शॉर्टकट जोड़ें" पर क्लिक करें।
                * **एक शॉर्टकट चलाना:**
                    * सूची दृश्य में: शॉर्टकट के बगल में "▶️" बटन पर क्लिक करें।
                    * **हॉटकी का उपयोग करना:** यदि एक वैश्विक हॉटकी असाइन की गई है और सेटिंग्स में सक्षम है, तो बस हॉटकी संयोजन दबाएं (उदा. `Ctrl+Alt+S`)।
                * **एक शॉर्टकट संपादित करना:**
                    * शॉर्टकट के बगल में (सूची दृश्य) "✏️" बटन पर क्लिक करें।
                    * संवाद बॉक्स में विवरण संशोधित करें और "परिवर्तन सहेजें" पर क्लिक करें।
                * **एक शॉर्टकट हटाना:**
                    * शॉर्टकट के बगल में (सूची दृश्य) "🗑️" बटन पर क्लिक करें।
                    * पुष्टि करें कि आप प्रॉम्प्ट में हटाना चाहते हैं।
                * **खोज और फ़िल्टर करना:**
                    * नाम या विवरण द्वारा शॉर्टकट खोजने के लिए खोज बार का उपयोग करें।
                    * प्रकार के अनुसार शॉर्टकट फ़िल्टर करने के लिए "श्रेणी" ड्रॉपडाउन का उपयोग करें।
                * **आयात/निर्यात (Import/Export):**
                    * "📤 निर्यात": अपने सभी शॉर्टकट डेटा को JSON फ़ाइल में सहेजें (बैकअप या माइग्रेशन के लिए उपयोगी)।
                    * "📥 आयात": JSON फ़ाइल से शॉर्टकट डेटा लोड करें। सावधान रहें, यह मौजूदा डेटा के साथ मर्ज हो जाएगा।

            3.  **एआई असिस्टेंट (🧠):**
                * यह टैब नए शॉर्टकट्स के लिए बुद्धिमान सुझाव प्रदान करता है, अनावश्यक शॉर्टकट्स की पहचान करता है, और टूटे हुए/अप्रयुक्त लिंक को साफ़ करने में मदद करता है।
                * **सुझाव उत्पन्न करें:** सामान्य एप्लिकेशन/वेबसाइटों के आधार पर नए शॉर्टकट्स के लिए विचार प्राप्त करें, और पुराने/अप्रयुक्त शॉर्टकट्स को साफ़ करने के लिए सुझाव प्राप्त करें।
                * **जोड़ें बटन:** शॉर्टकट को स्वचालित रूप से बनाने के लिए एक सुझाव के बगल में 'जोड़ें' पर क्लिक करें।
                * **अनावश्यक शॉर्टकट्स के लिए स्कैन करें:** जांचता है कि क्या आपके पास एक ही लक्ष्य पर इंगित करने वाले कई शॉर्टकट्स हैं, जिससे आपको व्यवस्थित करने में मदद मिलती है।
                * **टूटे हुए शॉर्टकट्स हटाएँ:** स्वचालित रूप से उन शॉर्टकट्स को हटा देता है जो अब वैध फ़ाइलों, फ़ोल्डरों या अनुप्रयोगों की ओर नहीं ले जाते हैं।
                * **अप्रयुक्त शॉर्टकट्स हटाएँ:** उन शॉर्टकट्स की पहचान करता है और उन्हें हटाने की पेशकश करता है जिनका लंबे समय से (उदा. 6 महीने) उपयोग नहीं किया गया है या जिनका उपयोग बहुत कम है।

            4.  **एनालिटिक्स (📈):**
                * यह टैब विस्तृत उपयोग आंकड़े प्रदान करता है, जिसमें कुल लॉन्च और श्रेणी वितरण शामिल हैं।
                * अपने "शीर्ष 5 सबसे अधिक उपयोग किए गए" शॉर्टकट्स देखें।
                * "उपयोग रुझान" पिछले 7 दिनों में आपके दैनिक शॉर्टकट लॉन्च का एक दृश्य (पाठ-आधारित) प्रतिनिधित्व प्रदान करता है।

            5.  **सेटिंग्स (⚙️):**
                * **थीम:** एप्लिकेशन के दृश्य थीम को बदलें (वर्तमान में केवल डार्क पूरी तरह से समर्थित है)।
                * **ऑटो-सेव:** अपने शॉर्टकट डेटा के स्वचालित सहेजे जाने को टॉगल करें।
                * **स्वचालित बैकअप:** अपने शॉर्टकट डेटा के स्वयंचलित बॅकअप को सक्षम/अक्षम करें (अंतिम 5 संस्करण रखता है)।
                * **ग्लोबल हॉटकीज़:** त्वरित पहुंच के लिए वैश्विक हॉटकीज़ को सक्षम/अक्षम करें। ('keyboard' Python लाइब्रेरी की आवश्यकता है: `pip install keyboard`)।

            --- टिप्स और ट्रिक्स ---
            * अपने शॉर्टकट नामों को वर्णनात्मक और याद रखने में आसान रखें।
            * निर्यात सुविधा का उपयोग करके नियमित रूप से अपने डेटा का बैकअप लें।
            * सिस्टम कमांड के लिए, लक्ष्य फ़ील्ड में सटीक कमांड स्ट्रिंग दर्ज करें (उदाहरण के लिए, नोटपैड के लिए `notepad.exe`, या विंडोज शटडाउन के लिए `shutdown /s /t 0` जैसे विशिष्ट शेल कमांड)।
            * शॉर्टकट के ठीक से काम करने के लिए सुनिश्चित करें कि लक्ष्य पथ/यूआरएल सही है।
            * हॉटकीज़ के लिए, `ctrl+alt+a`, `shift+f1` जैसे मानक संयोजनों का उपयोग करें। विरोधों को रोकने के लिए सामान्य सिस्टम हॉटकीज़ से बचें।

            एआई शॉर्टकट्स मैनेजर प्रो के साथ अपने कार्यप्रवाह को सुव्यवस्थित करने का आनंद लें!
            """,
            "Marathi": """
            एआय शॉर्टकट्स मॅनेजर प्रो मध्ये आपले स्वागत आहे!

            हे ॲप्लिकेशन तुम्हाला तुमच्या वारंवार वापरल्या जाणाऱ्या ॲप्लिकेशन्स, वेबसाइट्स, फाइल्स आणि फोल्डर्सना व्यवस्थित आणि त्वरित ॲक्सेस करण्यात मदत करते.

            --- हे ॲप कसे वापरावे ---

            1.  **डॅशबोर्ड (📊):**
                * तुमच्या शॉर्टकट्सचा आढावा घ्या, ज्यात एकूण संख्या, सर्वाधिक वापरलेले, श्रेणी आणि एकूण लॉन्च यांचा समावेश आहे.
                * तुमची अलीकडील शॉर्टकट ॲक्टिव्हिटी (गतिविधी) पहा.
                * एक नवीन शॉर्टकट त्वरित जोडण्यासाठी किंवा वापर अहवाल तयार करण्यासाठी "त्वरित क्रिया" (Quick Actions) वापरा.

            2.  **शॉर्टकट्स (⚡):**
                * **शॉर्टकट जोडणे:**
                    * "➕ जोडा" बटणावर क्लिक करा (किंवा डॅशबोर्डवरील "⚡ त्वरित जोडा" वर).
                    * "शॉर्टकट नाव", "टार्गेट पथ/URL", आणि पर्यायी "हॉटकी" भरा.
                    * पथ (पाथ) सहज निवडण्यासाठी "फाइल ब्राउझ करा" किंवा "फोल्डर ब्राउझ करा" वापरा.
                    * एक "श्रेणी" (ॲप्लिकेशन, वेबसाइट, फाइल, फोल्डर, सिस्टम) निवडा.
                    * स्पष्टतेसाठी एक "वर्णन" जोडा.
                    * "शॉर्टकट जोडा" वर क्लिक करा.
                * **शॉर्टकट चालवणे:**
                    * लिस्ट व्ह्यूमध्ये: शॉर्टकटच्या शेजारील "▶️" बटणावर क्लिक करा.
                    * **हॉटकी वापरणे:** जर ग्लोबल हॉटकी नियुक्त केली असेल आणि सेटिंग्जमध्ये सक्षम असेल, तर फक्त हॉटकी संयोजन दाबा (उदा. `Ctrl+Alt+S`).
                * **शॉर्टकट संपादित करणे:**
                    * शॉर्टकटच्या शेजारील (लिस्ट व्ह्यू) "✏️" बटणावर क्लिक करा.
                    * डायलॉगमध्ये तपशील बदला आणि "बदल जतन करा" वर क्लिक करा.
                * **शॉर्टकट हटवणे:**
                    * शॉर्टकटच्या शेजारील (लिस्ट व्ह्यू) "🗑️" बटणावर क्लिक करा.
                    * प्रॉम्प्टमध्ये हटवण्याची खात्री करा.
                * **शोध आणि फिल्टर करणे:**
                    * नाव किंवा वर्णनानुसार शॉर्टकट्स शोधण्यासाठी सर्च बार वापरा.
                    * प्रकारानुसार शॉर्टकट्स फिल्टर करण्यासाठी "श्रेणी" ड्रॉपडाउन वापरा.
                * **आयात/निर्यात (Import/Export):**
                    * "📤 निर्यात": तुमचा सर्व शॉर्टकट डेटा JSON फाइलमध्ये सेव्ह करा (बॅकअप किंवा स्थलांतरणासाठी उपयुक्त).
                    * "📥 आयात": JSON फाइलमधून शॉर्टकट डेटा लोड करा. काळजी घ्या, यामुळे विद्यमान डेटासह विलीन होईल.

            3.  **एआय असिस्टंट (🧠)::**
                * हा टॅब नवीन शॉर्टकट्ससाठी बुद्धिमान सूचना देतो, अनावश्यक शॉर्टकट्स ओळखतो आणि तुटलेले/अप्रयुक्त दुवे (लिंक्स) स्वच्छ करण्यात मदत करतो.
                * **सूचना तयार करा:** सामान्य ॲप्लिकेशन्स/वेबसाइट्सवर आधारित नवीन शॉर्टकट्ससाठी कल्पना मिळवा, आणि जुने/अप्रयुक्त शॉर्टकट्स स्वच्छ करण्यासाठी सूचना मिळवा.
                * **जोडा बटण:** शॉर्टकटला आपोआप तयार करण्यासाठी सूचनेच्या शेजारील 'जोडा' बटणावर क्लिक करा.
                * **अनावश्यक शॉर्टकट्ससाठी स्कॅन करा:** हे तपासते की तुमच्याकडे एकाच टार्गेटवर अनेक शॉर्टकट्स आहेत का, ज्यामुळे तुम्हाला व्यवस्थित करण्यास मदत होते.
                * **तुटलेले शॉर्टकट्स काढा:** हे आपोआप असे शॉर्टकट्स हटवते जे आता वैध फाइल्स, फोल्डर्स किंवा ॲप्लिकेशन्सकडे नेत नाहीत.
                * **अप्रयुक्त शॉर्टकट्स काढा:** असे शॉर्टकट्स ओळखतो आणि त्यांना हटवण्याची ऑफर देतो ज्यांचा दीर्घकाळापासून (उदा. 6 महिने) वापर केला गेला नाही किंवा ज्यांचा वापर खूप कमी आहे.

            4.  **ॲनालिटिक्स (�):**
                * हा टॅब तपशीलवार वापर आकडेवारी देतो, ज्यात एकूण लॉन्च आणि श्रेणी वितरण समाविष्ट आहे.
                * तुमचे "सर्वाधिक वापरलेले शीर्ष 5" शॉर्टकट्स पहा.
                * "वापर ट्रेंड" गेल्या 7 दिवसांतील तुमच्या दैनंदिन शॉर्टकट लॉन्चचे दृश्य (टेक्स्ट-आधारित) प्रतिनिधित्व प्रदान करतो.

            5.  **सेटिंग्ज (⚙️):**
                * **थीम:** ॲप्लिकेशनची दृश्य थीम बदला (सध्या फक्त डार्क थीम पूर्णपणे समर्थित आहे).
                * **ऑटो-सेव्ह:** तुमच्या शॉर्टकट डेटाचे स्वयंचलित जतन चालू/बंद करा.
                * **स्वयंचलित बॅकअप:** तुमच्या शॉर्टकट डेटाचे स्वयंचलित बॅकअप सक्षम/अक्षम करा (शेवटच्या 5 आवृत्त्या ठेवतो).
                * **ग्लोबल हॉटकीज:** त्वरित ॲक्सेससाठी ग्लोबल हॉटकीज सक्षम/अक्षम करा. ('keyboard' Python लायब्ररीची आवश्यकता आहे: `pip install keyboard`)।

            --- टिपा आणि युक्त्या ---
            * तुमचे शॉर्टकट नाव वर्णनात्मक आणि लक्षात ठेवण्यास सोपे ठेवा.
            * निर्यात वैशिष्ट्य वापरून तुमचा डेटा नियमितपणे बॅकअप करा.
            * सिस्टम कमांडसाठी, टार्गेट फील्डमध्ये अचूक कमांड स्ट्रिंग प्रविष्ट करा (उदा. नोटपॅडसाठी `notepad.exe` किंवा विंडोज शटडाउनसाठी `shutdown /s /t 0` सारखे विशिष्ट शेल कमांड).
            * शॉर्टकट योग्यरित्या कार्य करण्यासाठी टार्गेट पथ/URL योग्य असल्याची खात्री करा.
            * हॉटकीजसाठी, `ctrl+alt+a`, `shift+f1` सारखे मानक संयोजन वापरा. विरोधाभास टाळण्यासाठी सामान्य सिस्टम हॉटकीज टाळा.

            एआय शॉर्टकट्स मॅनेजर प्रो सह तुमचा कार्यप्रवाह सुव्यवस्थित करण्याचा आनंद घ्या!
            """
        }
        self.update_help_content() # Load initial content

    def update_help_content(self, *args):
        """Updates the help text based on the selected language."""
        selected_lang = self.help_lang_var.get()
        content = self.help_content.get(selected_lang, self.help_content["English"]) # Default to English
        self.help_text.delete("1.0", tk.END)
        self.help_text.insert("1.0", content)
        self.settings['help_language'] = selected_lang # Save selected language
        self.save_settings()


    def create_status_bar(self):
        """Creates a status bar at the bottom of the main window."""
        self.status_bar = tk.Label(self.root, text="Ready | Version 1.0",
                                   bd=1, relief=tk.SUNKEN, anchor=tk.W,
                                   bg=self.colors['background'], fg=self.colors['text_secondary'],
                                   font=('Segoe UI', 9))
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    # --- Shortcut Management Functions ---
    def show_add_shortcut_dialog(self):
        """Opens a dialog to add a new shortcut."""
        AddShortcutDialog(self.root, self.colors, self.add_shortcut)

    def add_shortcut(self, name, target, category, description, hotkey):
        """Adds a new shortcut to the data."""
        if name in self.shortcuts:
            messagebox.showwarning("Duplicate Shortcut", f"A shortcut named '{name}' already exists. Please choose a different name.")
            return False
        
        if hotkey and any(data.get('hotkey', '').lower() == hotkey.lower() for data in self.shortcuts.values()):
            messagebox.showwarning("Duplicate Hotkey", f"The hotkey '{hotkey}' is already assigned to another shortcut. Please choose a different hotkey.")
            return False

        self.shortcuts[name] = {
            'target': target,
            'category': category,
            'description': description,
            'hotkey': hotkey, # Store hotkey
            'usage_count': 0,
            'last_used': None,
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        self.save_shortcuts()
        self.refresh_shortcuts_view()
        self.update_dashboard_stats()
        self.refresh_analytics_data()
        self.update_status("Shortcut added successfully!")
        if GLOBAL_HOTKEYS_AVAILABLE and self.settings.get('hotkeys_enabled', True):
            self._start_hotkey_listener() # Re-register hotkeys
        return True

    def edit_shortcut_by_name(self, name):
        """Opens a dialog to edit an existing shortcut."""
        if name in self.shortcuts:
            shortcut_data = self.shortcuts[name]
            EditShortcutDialog(self.root, self.colors, name, shortcut_data, self.update_shortcut)
        else:
            messagebox.showerror("Error", "Shortcut not found.")

    def update_shortcut(self, original_name, new_name, target, category, description, hotkey):
        """Updates an existing shortcut in the data."""
        if original_name != new_name and new_name in self.shortcuts:
            messagebox.showwarning("Duplicate Shortcut", f"A shortcut named '{new_name}' already exists. Please choose a different name.")
            return False

        if hotkey and hotkey.lower() != self.shortcuts[original_name].get('hotkey', '').lower(): # If hotkey changed
            if any(data.get('hotkey', '').lower() == hotkey.lower() for n, data in self.shortcuts.items() if n != original_name):
                messagebox.showwarning("Duplicate Hotkey", f"The hotkey '{hotkey}' is already assigned to another shortcut. Please choose a different hotkey.")
                return False

        if original_name in self.shortcuts:
            data = self.shortcuts.pop(original_name)
            data.update({
                'target': target,
                'category': category,
                'description': description,
                'hotkey': hotkey, # Update hotkey
                'last_modified': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            self.shortcuts[new_name] = data
            self.save_shortcuts()
            self.refresh_shortcuts_view()
            self.update_dashboard_stats()
            self.refresh_analytics_data()
            self.update_status(f"Shortcut '{original_name}' updated to '{new_name}'.")
            if GLOBAL_HOTKEYS_AVAILABLE and self.settings.get('hotkeys_enabled', True):
                self._start_hotkey_listener() # Re-register hotkeys
            return True
        return False

    def delete_shortcut_by_name(self, name):
        """Deletes a shortcut from the data."""
        if messagebox.askyesno("Delete Shortcut", f"Are you sure you want to delete '{name}'? This action cannot be undone."):
            if name in self.shortcuts:
                del self.shortcuts[name]
                self.save_shortcuts()
                self.refresh_shortcuts_view()
                self.update_dashboard_stats()
                self.refresh_analytics_data()
                self.update_status(f"Shortcut '{name}' deleted.")
                if GLOBAL_HOTKEYS_AVAILABLE and self.settings.get('hotkeys_enabled', True):
                    self._start_hotkey_listener() # Re-register hotkeys
            else:
                messagebox.showerror("Error", "Shortcut not found.")

    def run_shortcut_by_name(self, name):
        """Executes a shortcut and updates its usage stats."""
        shortcut = self.shortcuts.get(name)
        if not shortcut:
            # This might happen if hotkey is pressed for a deleted shortcut before listener updates
            print(f"Attempted to run unknown shortcut: {name}")
            self.update_status(f"Error: Shortcut '{name}' not found.")
            return

        target = shortcut['target']
        category = shortcut['category']

        try:
            if category == 'Website':
                webbrowser.open_new_tab(target)
            elif category == 'Application' or category == 'File':
                if sys.platform == "win32":
                    os.startfile(target)
                else: # For Linux/macOS
                    subprocess.Popen(['xdg-open', target]) if sys.platform.startswith('linux') else subprocess.Popen(['open', target])
            elif category == 'Folder':
                if sys.platform == "win32":
                    os.startfile(target)
                else: # For Linux/macOS
                    subprocess.Popen(['xdg-open', target]) if sys.platform.startswith('linux') else subprocess.Popen(['open', target])
            elif category == 'System':
                # Use shell=True for system commands, but be cautious with security implications
                subprocess.Popen(target, shell=True)
            else:
                messagebox.showwarning("Unsupported Type", f"Cannot run shortcut of type: {category}")
                return

            # Update usage stats for the shortcut
            shortcut['usage_count'] = shortcut.get('usage_count', 0) + 1
            shortcut['last_used'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.save_shortcuts() # Save shortcut data after update

            # Update daily usage stats for analytics
            today_str = datetime.now().strftime('%Y-%m-%d')
            self.usage_stats[today_str] = self.usage_stats.get(today_str, 0) + 1
            self.save_analytics() # Save analytics data

            self.update_dashboard_stats()
            self.refresh_analytics_data() # Refresh analytics tab
            self.add_activity_log(f"Launched: {name}")
            self.update_status(f"Successfully launched '{name}'.")

        except Exception as e:
            messagebox.showerror("Execution Error", f"Failed to run '{name}': {e}")
            self.update_status(f"Error launching '{name}'.")

    def filter_shortcuts(self, *args):
        """Filters the shortcuts displayed in the view."""
        self.refresh_shortcuts_view()

    def export_shortcuts(self):
        """Exports all shortcuts to a JSON file."""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json")],
            title="Export Shortcuts Data"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.shortcuts, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("Export Success", "Shortcuts exported successfully!")
                self.update_status("Shortcuts exported.")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export shortcuts: {e}")
                self.update_status("Export failed.")

    def import_shortcuts(self):
        """Imports shortcuts from a JSON file."""
        file_path = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json")],
            title="Import Shortcuts Data"
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_data = json.load(f)

                for name, data in imported_data.items():
                    if name not in self.shortcuts:
                        self.shortcuts[name] = data
                    else:
                        # Merge logic: update existing, but preserve higher usage_count if importing older data
                        current_data = self.shortcuts[name]
                        imported_usage = data.get('usage_count', 0)
                        current_usage = current_data.get('usage_count', 0)
                        
                        self.shortcuts[name].update({
                            'target': data.get('target', current_data['target']),
                            'category': data.get('category', current_data['category']),
                            'description': data.get('description', current_data['description']),
                            'hotkey': data.get('hotkey', current_data.get('hotkey', '')), # Preserve or update hotkey
                            'usage_count': max(imported_usage, current_usage), # Keep higher usage count
                            'last_used': data.get('last_used', current_data['last_used']),
                            'created_at': data.get('created_at', current_data['created_at']),
                            'last_modified': datetime.now().strftime('%Y-%m-%d %H:%M:%S') # Mark as modified
                        })

                self.save_shortcuts()
                self.refresh_shortcuts_view()
                self.update_dashboard_stats()
                self.refresh_analytics_data()
                messagebox.showinfo("Import Success", "Shortcuts imported successfully!")
                self.update_status("Shortcuts imported.")
                if GLOBAL_HOTKEYS_AVAILABLE and self.settings.get('hotkeys_enabled', True):
                    self._start_hotkey_listener() # Re-register hotkeys
            except json.JSONDecodeError:
                messagebox.showerror("Import Error", "Invalid JSON file.")
                self.update_status("Import failed: invalid JSON.")
            except Exception as e:
                messagebox.showerror("Import Error", f"Failed to import shortcuts: {e}")
                self.update_status("Import failed.")

    def quick_add_shortcut(self):
        """A simplified dialog for quickly adding a shortcut (reusing the main add dialog)."""
        self.show_add_shortcut_dialog()

    def sync_shortcuts(self):
        """Placeholder for future cloud sync functionality."""
        messagebox.showinfo("Sync", "Cloud synchronization feature coming soon!")
        self.update_status("Sync initiated (feature pending).")

    def backup_shortcuts(self):
        """Triggers an immediate backup of shortcuts data."""
        self.save_shortcuts() # This method already includes backup logic if enabled
        messagebox.showinfo("Backup", "Shortcuts data backed up successfully!")
        self.update_status("Manual backup completed.")

    def search_installed_apps(self):
        """Opens a dialog to search for and add common applications as shortcuts."""
        SearchAppsDialog(self.root, self.colors, self.add_shortcut, self.shortcuts)
        self.update_status("Opened application search dialog.")

    # --- Dashboard and Analytics Functions ---
    def update_dashboard_stats(self):
        """Updates the statistics displayed on the dashboard."""
        total_shortcuts = len(self.shortcuts)
        self.stats_card_0.config(text=str(total_shortcuts))

        if total_shortcuts > 0:
            # Calculate most used shortcut
            most_used_shortcut = None
            max_usage = -1
            for name, data in self.shortcuts.items():
                count = data.get('usage_count', 0)
                if count > max_usage:
                    max_usage = count
                    most_used_shortcut = name
            
            self.stats_card_1.config(text=f"{most_used_shortcut} ({max_usage} uses)" if most_used_shortcut else "None")

            # Calculate unique categories
            categories = set(data.get('category', 'General') for data in self.shortcuts.values())
            self.stats_card_2.config(text=str(len(categories)))
            
            # Calculate total launches across all shortcuts
            total_launches = sum(data.get('usage_count', 0) for data in self.shortcuts.values())
            self.stats_card_3.config(text=str(total_launches))
        else:
            self.stats_card_1.config(text="None")
            self.stats_card_2.config(text="0")
            self.stats_card_3.config(text="0")

        self.refresh_recent_activity()

    def refresh_recent_activity(self):
        """Populates the recent activity listbox on the dashboard."""
        self.activity_listbox.delete(0, tk.END)
        # Sort shortcuts by last_used date in descending order
        sorted_shortcuts = sorted(
            [s for s in self.shortcuts.values() if s.get('last_used')],
            key=lambda x: datetime.strptime(x['last_used'], '%Y-%m-%d %H:%M:%S'),
            reverse=True
        )
        for i, shortcut in enumerate(sorted_shortcuts[:10]): # Show top 10 recent activities
            name = next((k for k, v in self.shortcuts.items() if v == shortcut), "Unknown") # Find original name
            last_used_dt = datetime.strptime(shortcut['last_used'], '%Y-%m-%d %H:%M:%S')
            self.activity_listbox.insert(tk.END, f"{last_used_dt.strftime('%Y-%m-%d %H:%M')} - Launched: {name}")

    def generate_usage_report(self):
        """Generates a detailed usage report and opens it in a text editor."""
        report_content = "AI Shortcuts Manager Pro Usage Report\n\n"
        report_content += f"Generated On: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        report_content += "--- Shortcut Statistics ---\n"

        total_shortcuts = len(self.shortcuts)
        total_launches = sum(data.get('usage_count', 0) for data in self.shortcuts.values())
        categories = set(data.get('category', 'General') for data in self.shortcuts.values())

        report_content += f"Total Shortcuts: {total_shortcuts}\n"
        report_content += f"Total Launches: {total_launches}\n"
        report_content += f"Unique Categories: {len(categories)}\n\n"

        report_content += "--- Most Used Shortcuts ---\n"
        sorted_by_usage = sorted(
            self.shortcuts.items(),
            key=lambda item: item[1].get('usage_count', 0),
            reverse=True
        )
        for name, data in sorted_by_usage[:5]: # Top 5 most used
            report_content += f"- {name}: {data.get('usage_count', 0)} times (Last used: {data.get('last_used', 'N/A')})\n"
        report_content += "\n"

        report_content += "--- Daily Launch Trends (Last 7 Days) ---\n"
        # Ensure usage_stats is sorted by date for consistent reporting
        daily_launches_sorted = sorted(self.usage_stats.items(), reverse=True)[:7]
        for date_str, count in reversed(daily_launches_sorted): # Display oldest first
            report_content += f"- {date_str}: {count} launches\n"
        report_content += "\n"


        report_content += "--- All Shortcuts Details ---\n"
        for name, data in self.shortcuts.items():
            report_content += f"Name: {name}\n"
            report_content += f"  Target: {data.get('target', 'N/A')}\n"
            report_content += f"  Category: {data.get('category', 'General')}\n"
            report_content += f"  Description: {data.get('description', 'N/A')}\n"
            report_content += f"  Hotkey: {data.get('hotkey', 'N/A')}\n" # Include hotkey in report
            report_content += f"  Usage Count: {data.get('usage_count', 0)}\n"
            report_content += f"  Last Used: {data.get('last_used', 'N/A')}\n"
            report_content += f"  Created At: {data.get('created_at', 'N/A')}\n"
            report_content += f"  Last Modified: {data.get('last_modified', 'N/A')}\n"
            report_content += "---\n"

        # Save to a temporary file and open it with default text editor
        report_file_path = self.app_dir / f"usage_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(report_file_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            os.startfile(report_file_path) # Opens with default text editor on Windows
            messagebox.showinfo("Report Generated", f"Usage report saved to:\n{report_file_path}")
            self.update_status("Report generated.")
        except Exception as e:
            messagebox.showerror("Report Error", f"Failed to generate report: {e}")
            self.update_status("Report generation failed.")

    # --- System & Utility Functions ---
    def load_system_info(self):
        """Loads system information and performs initial UI updates."""
        self.system_info = {
            'OS': sys.platform,
            'Python Version': sys.version,
            'App Directory': str(self.app_dir),
            'Total Shortcuts Loaded': len(self.shortcuts)
        }
        # Perform initial UI updates based on loaded data
        self.update_dashboard_stats()
        self.refresh_recent_activity()
        self.refresh_analytics_data()
        self.generate_ai_suggestions(update_dashboard=True) # Initial AI suggestions for dashboard
        self.update_status("System information loaded. Ready.")

    def auto_save_timer(self):
        """Sets up a recurring timer for auto-saving data."""
        if self.settings.get('auto_save', True):
            self.save_shortcuts()
            self.save_analytics()
            self.update_status("Auto-saved data.")
        self.root.after(300000, self.auto_save_timer) # Save every 5 minutes (300,000 ms)

    def refresh_all_data(self):
        """Refreshes all data and UI elements across the application."""
        self.shortcuts = self.load_shortcuts()
        self.settings = self.load_settings()
        self.usage_stats = self.load_analytics()
        self.refresh_shortcuts_view()
        self.update_dashboard_stats()
        self.refresh_analytics_data()
        self.generate_ai_suggestions(update_dashboard=True) # Refresh AI suggestions as well
        if GLOBAL_HOTKEYS_AVAILABLE and self.settings.get('hotkeys_enabled', True):
            self._start_hotkey_listener() # Re-register hotkeys after data refresh
        self.update_status("All data refreshed.")

    def update_status(self, message):
        """Updates the message in the status bar with a timestamp."""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.status_bar.config(text=f"{message} | {timestamp}")

    def add_activity_log(self, activity):
        """Adds an activity entry to the recent activity list on the dashboard."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"{timestamp} - {activity}"
        
        self.activity_listbox.insert(0, log_entry) # Insert at the top
        # Keep only the last 10 entries in the displayed list
        if self.activity_listbox.size() > 10:
            self.activity_listbox.delete(10, tk.END)

    def run(self):
        """Starts the Tkinter event loop, running the application."""
        self.root.mainloop()

# --- Dialogs for Shortcut Management ---

class AddShortcutDialog:
    def __init__(self, parent, colors, add_callback):
        self.parent = parent
        self.colors = colors
        self.add_callback = add_callback

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add New Shortcut")
        self.dialog.transient(parent) # Make dialog transient to parent
        self.dialog.grab_set() # Grab focus, prevent interaction with parent
        self.dialog.resizable(False, False) # Dialogs typically not resizable
        self.dialog.configure(bg=self.colors['primary'])

        self.setup_ui()
        # Center the dialog after widgets are created and sized
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        self.name_entry.focus_set() # Set focus to the first input field

    def setup_ui(self):
        """Sets up the UI for adding a new shortcut."""
        form_frame = tk.Frame(self.dialog, bg=self.colors['primary'], padx=20, pady=20)
        form_frame.pack(fill=tk.BOTH, expand=True)

        # Shortcut Name
        tk.Label(form_frame, text="Shortcut Name:", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        self.name_entry = tk.Entry(form_frame, bg=self.colors['surface'], fg=self.colors['text'],
                                   insertbackground=self.colors['text'], relief=tk.FLAT, font=('Segoe UI', 10))
        self.name_entry.pack(fill=tk.X, pady=(0,10))

        # Target Path/URL
        tk.Label(form_frame, text="Target Path/URL:", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        self.target_entry = tk.Entry(form_frame, bg=self.colors['surface'], fg=self.colors['text'],
                                     insertbackground=self.colors['text'], relief=tk.FLAT, font=('Segoe UI', 10))
        self.target_entry.pack(fill=tk.X)

        # Browse buttons for file/folder
        browse_frame = tk.Frame(form_frame, bg=self.colors['primary'])
        browse_frame.pack(fill=tk.X, pady=(5,10))
        tk.Button(browse_frame, text="Browse File", command=self.browse_file,
                  bg=self.colors['secondary'], fg=self.colors['text'], relief=tk.FLAT).pack(side=tk.LEFT, padx=2, expand=True, fill=tk.X)
        tk.Button(browse_frame, text="Browse Folder", command=self.browse_folder,
                  bg=self.colors['secondary'], fg=self.colors['text'], relief=tk.FLAT).pack(side=tk.LEFT, padx=2, expand=True, fill=tk.X)

        # Category selection
        tk.Label(form_frame, text="Category:", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        self.category_var = tk.StringVar(value="Application")
        self.category_combo = ttk.Combobox(form_frame, textvariable=self.category_var,
                                          values=["Application", "Website", "File", "Folder", "System"],
                                          state="readonly", font=('Segoe UI', 10), style='TCombobox')
        self.category_combo.pack(fill=tk.X, pady=(0,10))

        # Hotkey input
        tk.Label(form_frame, text="Hotkey (e.g., ctrl+alt+s):", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        self.hotkey_entry = tk.Entry(form_frame, bg=self.colors['surface'], fg=self.colors['text'],
                                     insertbackground=self.colors['text'], relief=tk.FLAT, font=('Segoe UI', 10))
        self.hotkey_entry.pack(fill=tk.X, pady=(0,10))
        # Add a button to record hotkey if keyboard library is available
        if GLOBAL_HOTKEYS_AVAILABLE:
            tk.Button(form_frame, text="Record Hotkey", command=self.record_hotkey,
                      bg=self.colors['secondary'], fg=self.colors['text'], relief=tk.FLAT).pack(anchor='w', pady=(0,5))
            tk.Label(form_frame, text="Press desired key combination. Click 'Record Hotkey' again to stop.",
                     font=('Segoe UI', 8, 'italic'), fg=self.colors['text_secondary'], bg=self.colors['primary']).pack(anchor='w')


        # Description
        tk.Label(form_frame, text="Description:", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        self.description_text = tk.Text(form_frame, height=4, bg=self.colors['surface'], fg=self.colors['text'],
                                        insertbackground=self.colors['text'], relief=tk.FLAT, font=('Segoe UI', 10))
        self.description_text.pack(fill=tk.BOTH, expand=True, pady=(0,10))

        # Action buttons (Add/Cancel)
        button_frame = tk.Frame(self.dialog, bg=self.colors['primary'])
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        tk.Button(button_frame, text="Add Shortcut", command=self.add_and_close,
                  bg=self.colors['success'], fg='white', relief=tk.FLAT, padx=15, pady=8, cursor='hand2').pack(side=tk.RIGHT, padx=5)
        tk.Button(button_frame, text="Cancel", command=self.dialog.destroy,
                  bg=self.colors['danger'], fg='white', relief=tk.FLAT, padx=15, pady=8, cursor='hand2').pack(side=tk.RIGHT)

    def browse_file(self):
        """Opens a file dialog and inserts the selected path into the target entry."""
        file_path = filedialog.askopenfilename()
        if file_path:
            self.target_entry.delete(0, tk.END)
            self.target_entry.insert(0, file_path)
            # Try to infer category based on file extension
            if file_path.lower().endswith(('.exe', '.bat', '.sh', '.app')):
                self.category_var.set("Application")
            else:
                self.category_var.set("File")

    def browse_folder(self):
        """Opens a folder dialog and inserts the selected path into the target entry."""
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.target_entry.delete(0, tk.END)
            self.target_entry.insert(0, folder_path)
            self.category_var.set("Folder")

    def record_hotkey(self):
        """Starts/stops recording a hotkey from user input."""
        if not GLOBAL_HOTKEYS_AVAILABLE:
            messagebox.showwarning("Hotkey Recording", "The 'keyboard' library is not installed. Hotkey recording is unavailable.")
            return

        if hasattr(self, '_hotkey_recorder_hook') and self._hotkey_recorder_hook:
            # Stop recording
            keyboard.unhook(self._hotkey_recorder_hook)
            self._hotkey_recorder_hook = None
            self.hotkey_entry.config(state=tk.NORMAL)
            messagebox.showinfo("Hotkey Recording", "Hotkey recording stopped.")
            return

        self.hotkey_entry.delete(0, tk.END)
        self.hotkey_entry.insert(0, "Press your hotkey...")
        self.hotkey_entry.config(state=tk.DISABLED) # Disable entry during recording

        def on_key_event(event):
            # Only capture key down events for combinations
            if event.event_type == keyboard.KEY_DOWN:
                current_hotkey = keyboard.get_hotkey_name()
                if current_hotkey:
                    self.hotkey_entry.config(state=tk.NORMAL)
                    self.hotkey_entry.delete(0, tk.END)
                    self.hotkey_entry.insert(0, current_hotkey)
                    keyboard.unhook(self._hotkey_recorder_hook)
                    self._hotkey_recorder_hook = None
                    messagebox.showinfo("Hotkey Recorded", f"Hotkey '{current_hotkey}' recorded. Click 'Record Hotkey' again to clear/re-record.")
                    return False # Stop listening after first combination
            return True # Continue listening

        self._hotkey_recorder_hook = keyboard.hook(on_key_event)
        messagebox.showinfo("Hotkey Recording", "Press your desired hotkey combination now. (e.g., Ctrl+Alt+A)")


    def add_and_close(self):
        """Collects data from fields, calls the add_callback, and closes the dialog."""
        name = self.name_entry.get().strip()
        target = self.target_entry.get().strip()
        category = self.category_var.get()
        description = self.description_text.get("1.0", tk.END).strip()
        hotkey = self.hotkey_entry.get().strip() if GLOBAL_HOTKEYS_AVAILABLE else ""
        if hotkey == "Press your hotkey...": hotkey = "" # Clear placeholder

        if not name or not target:
            messagebox.showwarning("Input Error", "Name and Target cannot be empty.")
            return

        if self.add_callback(name, target, category, description, hotkey):
            self.dialog.destroy()


class EditShortcutDialog:
    def __init__(self, parent, colors, original_name, shortcut_data, update_callback):
        self.parent = parent
        self.colors = colors
        self.original_name = original_name
        self.shortcut_data = shortcut_data
        self.update_callback = update_callback

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"Edit Shortcut: {original_name}")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        self.dialog.resizable(False, False)
        self.dialog.configure(bg=self.colors['primary'])

        self.setup_ui()
        self.load_data()
        # Center the dialog
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        self.name_entry.focus_set()

    def setup_ui(self):
        """Sets up the UI for editing an existing shortcut."""
        form_frame = tk.Frame(self.dialog, bg=self.colors['primary'], padx=20, pady=20)
        form_frame.pack(fill=tk.BOTH, expand=True)

        tk.Label(form_frame, text="Shortcut Name:", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        self.name_entry = tk.Entry(form_frame, bg=self.colors['surface'], fg=self.colors['text'],
                                   insertbackground=self.colors['text'], relief=tk.FLAT, font=('Segoe UI', 10))
        self.name_entry.pack(fill=tk.X, pady=(0,10))

        tk.Label(form_frame, text="Target Path/URL:", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        self.target_entry = tk.Entry(form_frame, bg=self.colors['surface'], fg=self.colors['text'],
                                     insertbackground=self.colors['text'], relief=tk.FLAT, font=('Segoe UI', 10))
        self.target_entry.pack(fill=tk.X)

        browse_frame = tk.Frame(form_frame, bg=self.colors['primary'])
        browse_frame.pack(fill=tk.X, pady=(5,10))
        tk.Button(browse_frame, text="Browse File", command=self.browse_file,
                  bg=self.colors['secondary'], fg=self.colors['text'], relief=tk.FLAT).pack(side=tk.LEFT, padx=2, expand=True, fill=tk.X)
        tk.Button(browse_frame, text="Browse Folder", command=self.browse_folder,
                  bg=self.colors['secondary'], fg=self.colors['text'], relief=tk.FLAT).pack(side=tk.LEFT, padx=2, expand=True, fill=tk.X)

        tk.Label(form_frame, text="Category:", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(form_frame, textvariable=self.category_var,
                                          values=["Application", "Website", "File", "Folder", "System"],
                                          state="readonly", font=('Segoe UI', 10), style='TCombobox')
        self.category_combo.pack(fill=tk.X, pady=(0,10))

        # Hotkey input
        tk.Label(form_frame, text="Hotkey (e.g., ctrl+alt+s):", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        self.hotkey_entry = tk.Entry(form_frame, bg=self.colors['surface'], fg=self.colors['text'],
                                     insertbackground=self.colors['text'], relief=tk.FLAT, font=('Segoe UI', 10))
        self.hotkey_entry.pack(fill=tk.X, pady=(0,10))
        if GLOBAL_HOTKEYS_AVAILABLE:
            tk.Button(form_frame, text="Record Hotkey", command=self.record_hotkey,
                      bg=self.colors['secondary'], fg=self.colors['text'], relief=tk.FLAT).pack(anchor='w', pady=(0,5))
            tk.Label(form_frame, text="Press desired key combination. Click 'Record Hotkey' again to stop.",
                     font=('Segoe UI', 8, 'italic'), fg=self.colors['text_secondary'], bg=self.colors['primary']).pack(anchor='w')


        tk.Label(form_frame, text="Description:", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        self.description_text = tk.Text(form_frame, height=4, bg=self.colors['surface'], fg=self.colors['text'],
                                        insertbackground=self.colors['text'], relief=tk.FLAT, font=('Segoe UI', 10))
        self.description_text.pack(fill=tk.BOTH, expand=True, pady=(0,10))

        button_frame = tk.Frame(self.dialog, bg=self.colors['primary'])
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Button(button_frame, text="Save Changes", command=self.save_and_close,
                  bg=self.colors['accent'], fg='white', relief=tk.FLAT, padx=15, pady=8, cursor='hand2').pack(side=tk.RIGHT, padx=5)
        tk.Button(button_frame, text="Cancel", command=self.dialog.destroy,
                  bg=self.colors['danger'], fg='white', relief=tk.FLAT, padx=15, pady=8, cursor='hand2').pack(side=tk.RIGHT)

    def load_data(self):
        """Loads existing shortcut data into the dialog fields."""
        self.name_entry.insert(0, self.original_name)
        self.target_entry.insert(0, self.shortcut_data.get('target', ''))
        self.category_var.set(self.shortcut_data.get('category', 'Application'))
        self.description_text.insert("1.0", self.shortcut_data.get('description', ''))
        self.hotkey_entry.insert(0, self.shortcut_data.get('hotkey', ''))

    def browse_file(self):
        """Opens a file dialog and inserts the selected path into the target entry."""
        file_path = filedialog.askopenfilename()
        if file_path:
            self.target_entry.delete(0, tk.END)
            self.target_entry.insert(0, file_path)
            if file_path.lower().endswith(('.exe', '.bat', '.sh', '.app')):
                self.category_var.set("Application")
            else:
                self.category_var.set("File")

    def browse_folder(self):
        """Opens a folder dialog and inserts the selected path into the target entry."""
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.target_entry.delete(0, tk.END)
            self.target_entry.insert(0, folder_path)
            self.category_var.set("Folder")

    def record_hotkey(self):
        """Starts/stops recording a hotkey from user input."""
        if not GLOBAL_HOTKEYS_AVAILABLE:
            messagebox.showwarning("Hotkey Recording", "The 'keyboard' library is not installed. Hotkey recording is unavailable.")
            return

        if hasattr(self, '_hotkey_recorder_hook') and self._hotkey_recorder_hook:
            # Stop recording
            keyboard.unhook(self._hotkey_recorder_hook)
            self._hotkey_recorder_hook = None
            self.hotkey_entry.config(state=tk.NORMAL)
            messagebox.showinfo("Hotkey Recording", "Hotkey recording stopped.")
            return

        self.hotkey_entry.delete(0, tk.END)
        self.hotkey_entry.insert(0, "Press your hotkey...")
        self.hotkey_entry.config(state=tk.DISABLED)

        def on_key_event(event):
            if event.event_type == keyboard.KEY_DOWN:
                current_hotkey = keyboard.get_hotkey_name()
                if current_hotkey:
                    self.hotkey_entry.config(state=tk.NORMAL)
                    self.hotkey_entry.delete(0, tk.END)
                    self.hotkey_entry.insert(0, current_hotkey)
                    keyboard.unhook(self._hotkey_recorder_hook)
                    self._hotkey_recorder_hook = None
                    messagebox.showinfo("Hotkey Recorded", f"Hotkey '{current_hotkey}' recorded. Click 'Record Hotkey' again to clear/re-record.")
                    return False
            return True

        self._hotkey_recorder_hook = keyboard.hook(on_key_event)
        messagebox.showinfo("Hotkey Recording", "Press your desired hotkey combination now. (e.g., Ctrl+Alt+A)")


    def save_and_close(self):
        """Collects data from fields, calls the update_callback, and closes the dialog."""
        new_name = self.name_entry.get().strip()
        target = self.target_entry.get().strip()
        category = self.category_var.get()
        description = self.description_text.get("1.0", tk.END).strip()
        hotkey = self.hotkey_entry.get().strip() if GLOBAL_HOTKEYS_AVAILABLE else ""
        if hotkey == "Press your hotkey...": hotkey = ""

        if not new_name or not target:
            messagebox.showwarning("Input Error", "Name and Target cannot be empty.")
            return
        
        if self.update_callback(self.original_name, new_name, target, category, description, hotkey):
            self.dialog.destroy()

class SearchAppsDialog:
    def __init__(self, parent, colors, add_shortcut_callback, existing_shortcuts):
        self.parent = parent
        self.colors = colors
        self.add_shortcut_callback = add_shortcut_callback
        self.existing_shortcuts = existing_shortcuts # Pass existing shortcuts to avoid duplicates

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Search & Add Applications")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        self.dialog.resizable(False, False)
        self.dialog.configure(bg=self.colors['primary'])

        self.applications = self._get_common_applications() # Pre-populate with common apps
        self.filtered_applications = list(self.applications.keys()) # For search/filter

        self.setup_ui()
        self.dialog.update_idletasks()
        x = parent.winfo_x() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_y() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        self.search_entry.focus_set()
        self._populate_app_list()

    def setup_ui(self):
        """Sets up the UI for the application search dialog."""
        main_frame = tk.Frame(self.dialog, bg=self.colors['primary'], padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        tk.Label(main_frame, text="Search Applications:", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))
        
        search_frame = tk.Frame(main_frame, bg=self.colors['primary'])
        search_frame.pack(fill=tk.X, pady=(0,10))
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self._filter_app_list)
        self.search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                                     bg=self.colors['surface'], fg=self.colors['text'],
                                     insertbackground=self.colors['text'], relief=tk.FLAT, font=('Segoe UI', 10))
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Label(search_frame, text="🔍", font=('Segoe UI', 14),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(side=tk.RIGHT, padx=5)


        tk.Label(main_frame, text="Available Applications:", font=('Segoe UI', 10),
                 bg=self.colors['primary'], fg=self.colors['text']).pack(anchor='w', pady=(5,0))

        # Treeview for displaying applications
        self.app_tree = ttk.Treeview(main_frame, columns=("Name", "Path"), show="headings",
                                     selectmode="browse", style='Treeview')
        self.app_tree.heading("Name", text="Application Name")
        self.app_tree.heading("Path", text="Path/Target")
        self.app_tree.column("Name", width=150, stretch=tk.YES)
        self.app_tree.column("Path", width=250, stretch=tk.YES)
        self.app_tree.pack(fill=tk.BOTH, expand=True, pady=(0,10))

        # Configure Treeview style
        self.style = ttk.Style()
        self.style.configure('Treeview',
                             background=self.colors['background'],
                             foreground=self.colors['text'],
                             fieldbackground=self.colors['background'],
                             font=('Segoe UI', 9),
                             rowheight=25)
        self.style.map('Treeview',
                       background=[('selected', self.colors['accent'])],
                       foreground=[('selected', 'white')])
        self.style.configure('Treeview.Heading',
                             background=self.colors['surface'],
                             foreground=self.colors['text'],
                             font=('Segoe UI', 10, 'bold'))


        # Scrollbar for Treeview
        tree_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.app_tree.yview, style='Vertical.TScrollbar')
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.app_tree.configure(yscrollcommand=tree_scrollbar.set)

        # Action buttons
        button_frame = tk.Frame(self.dialog, bg=self.colors['primary'])
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        tk.Button(button_frame, text="Add Selected as Shortcut", command=self._add_selected_app,
                  bg=self.colors['success'], fg='white', relief=tk.FLAT, padx=15, pady=8, cursor='hand2').pack(side=tk.RIGHT, padx=5)
        tk.Button(button_frame, text="Close", command=self.dialog.destroy,
                  bg=self.colors['danger'], fg='white', relief=tk.FLAT, padx=15, pady=8, cursor='hand2').pack(side=tk.RIGHT)

    def _get_common_applications(self):
        """Returns a dictionary of common applications and their typical targets."""
        common_apps = {
            "Notepad": {"target": "notepad.exe", "category": "Application", "description": "Windows Notepad text editor."},
            "Calculator": {"target": "calc.exe", "category": "Application", "description": "Windows Calculator."},
            "Command Prompt": {"target": "cmd.exe", "category": "Application", "description": "Windows Command Prompt."},
            "File Explorer": {"target": "explorer.exe", "category": "Application", "description": "Open Windows File Explorer."},
            "Control Panel": {"target": "control.exe", "category": "Application", "description": "Open Windows Control Panel."},
            "Google Chrome": {"target": "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "category": "Application", "description": "Google Chrome web browser. (Default path, may vary)"},
            "Mozilla Firefox": {"target": "C:\\Program Files\\Mozilla Firefox\\firefox.exe", "category": "Application", "description": "Mozilla Firefox web browser. (Default path, may vary)"},
            "Microsoft Edge": {"target": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe", "category": "Application", "description": "Microsoft Edge web browser. (Default path, may vary)"},
            "PowerShell": {"target": "powershell.exe", "category": "Application", "description": "Windows PowerShell."},
            "Paint": {"target": "mspaint.exe", "category": "Application", "description": "Microsoft Paint."},
            "Task Manager": {"target": "taskmgr.exe", "category": "Application", "description": "Open Task Manager."},
            "WordPad": {"target": "wordpad.exe", "category": "Application", "description": "Windows WordPad rich text editor."},
            "Registry Editor": {"target": "regedit.exe", "category": "Application", "description": "Open Windows Registry Editor."},
            "Disk Management": {"target": "diskmgmt.msc", "category": "System", "description": "Open Disk Management console."},
            "Services": {"target": "services.msc", "category": "System", "description": "Open Windows Services console."},
            "Device Manager": {"target": "devmgmt.msc", "category": "System", "description": "Open Windows Device Manager."}
        }
        
        # Filter out apps that already exist as shortcuts
        filtered_apps = {}
        for name, data in common_apps.items():
            # Check if the target already exists in the main application's shortcuts
            target_exists = False
            for existing_shortcut_data in self.existing_shortcuts.values():
                if existing_shortcut_data['target'].lower() == data['target'].lower():
                    target_exists = True
                    break
            if not target_exists:
                filtered_apps[name] = data
        
        return filtered_apps

    def _populate_app_list(self):
        """Populates the Treeview with the filtered application list."""
        self.app_tree.delete(*self.app_tree.get_children()) # Clear existing items
        search_term = self.search_var.get().lower()

        for app_name, app_data in self.applications.items():
            if search_term in app_name.lower() or search_term in app_data['target'].lower():
                self.app_tree.insert("", "end", values=(app_name, app_data['target']))

    def _filter_app_list(self, *args):
        """Filters the application list based on search input."""
        self._populate_app_list() # Re-populate with current filter

    def _add_selected_app(self):
        """Adds the selected application from the list as a shortcut."""
        selected_item = self.app_tree.focus()
        if not selected_item:
            messagebox.showwarning("No Selection", "Please select an application from the list.")
            return

        values = self.app_tree.item(selected_item, 'values')
        app_name = values[0]
        app_target = values[1]

        # Retrieve full data from the original applications dictionary
        app_data = self.applications.get(app_name)
        if not app_data:
            messagebox.showerror("Error", "Could not retrieve full details for the selected application.")
            return

        # Confirm with the user before adding
        response = messagebox.askyesno("Confirm Add Shortcut",
                                       f"Add '{app_name}' as a new shortcut?\n"
                                       f"Target: {app_target}\n"
                                       f"Category: {app_data['category']}\n"
                                       f"Description: {app_data['description']}")
        
        if response:
            # Call the main app's add_shortcut function
            success = self.add_shortcut_callback(
                app_name,
                app_target,
                app_data['category'],
                app_data['description'],
                hotkey="" # No hotkey by default from this dialog
            )
            if success:
                messagebox.showinfo("Shortcut Added", f"'{app_name}' added successfully to your shortcuts!")
                # Remove the added app from the suggestions in this dialog
                del self.applications[app_name]
                self._populate_app_list() # Refresh the list
            else:
                messagebox.showerror("Add Error", f"Failed to add '{app_name}'. It might already exist or there was another issue.")


# --- Main Application Execution ---
if __name__ == "__main__":
    app = ModernAIShortcutsManager()
    app.run()
