# Core dependencies for AI PC Shortcuts Manager Pro v2.0.0
keyboard>=1.13.0
psutil>=5.9.0
requests>=2.28.0

# AI and Machine Learning dependencies
openai>=1.0.0
nltk>=3.8
scikit-learn>=1.3.0
numpy>=1.24.0

# Voice Recognition and TTS
SpeechRecognition>=3.10.0
pyttsx3>=2.90

# System Tray Integration
pystray>=0.19.0
Pillow>=10.0.0

# Advanced UI Components
customtkinter>=5.0.0

# Optional dependencies for enhanced features
# Uncomment if you want these features:

# For cloud features
# boto3>=1.26.0
# dropbox>=11.36.0

# For advanced analytics and visualization
# matplotlib>=3.6.0
# pandas>=1.5.0
# seaborn>=0.12.0

# For system integration (Windows)
# pywin32>=305  # Windows only

# For web scraping (future feature)
# beautifulsoup4>=4.12.0
# selenium>=4.15.0

# Development dependencies
# pytest>=7.0.0
# black>=22.0.0
# flake8>=5.0.0
# mypy>=1.5.0
